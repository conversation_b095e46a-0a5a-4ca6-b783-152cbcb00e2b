"""
Sound Effects for J.A.R.V.I.S. Pomodoro Timer
Handles audio feedback and ambient sounds
"""

import pygame
import threading
import time
import math
import numpy as np
from typing import Optional, Dict
import io


class SoundGenerator:
    """Generates synthetic J.A.R.V.I.S.-style sound effects"""
    
    def __init__(self, sample_rate: int = 22050):
        self.sample_rate = sample_rate
        pygame.mixer.pre_init(frequency=sample_rate, size=-16, channels=2, buffer=512)
        pygame.mixer.init()
        
        # Cache for generated sounds
        self.sound_cache: Dict[str, pygame.mixer.Sound] = {}
    
    def generate_notification_sound(self, frequency: float = 440, duration: float = 0.5) -> pygame.mixer.Sound:
        """Generate a J.A.R.V.I.S.-style notification sound"""
        cache_key = f"notification_{frequency}_{duration}"
        if cache_key in self.sound_cache:
            return self.sound_cache[cache_key]
        
        frames = int(duration * self.sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            t = i / self.sample_rate
            
            # Main tone with harmonic
            wave = np.sin(2 * np.pi * frequency * t)
            wave += 0.3 * np.sin(2 * np.pi * frequency * 2 * t)  # Octave
            wave += 0.2 * np.sin(2 * np.pi * frequency * 1.5 * t)  # Fifth
            
            # Envelope (fade in/out)
            envelope = 1.0
            fade_time = 0.1
            if t < fade_time:
                envelope = t / fade_time
            elif t > duration - fade_time:
                envelope = (duration - t) / fade_time
            
            # Modulation for J.A.R.V.I.S. effect
            modulation = 1 + 0.1 * np.sin(2 * np.pi * 5 * t)
            
            sample = wave * envelope * modulation * 0.3
            arr[i] = [sample, sample]
        
        # Convert to pygame sound
        sound_array = (arr * 32767).astype(np.int16)
        sound = pygame.sndarray.make_sound(sound_array)
        
        self.sound_cache[cache_key] = sound
        return sound
    
    def generate_startup_sound(self) -> pygame.mixer.Sound:
        """Generate J.A.R.V.I.S. startup sound"""
        cache_key = "startup"
        if cache_key in self.sound_cache:
            return self.sound_cache[cache_key]
        
        duration = 2.0
        frames = int(duration * self.sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            t = i / self.sample_rate
            
            # Frequency sweep
            start_freq = 200
            end_freq = 800
            freq = start_freq + (end_freq - start_freq) * (t / duration)
            
            # Main wave
            wave = np.sin(2 * np.pi * freq * t)
            
            # Add harmonics
            wave += 0.3 * np.sin(2 * np.pi * freq * 1.5 * t)
            wave += 0.2 * np.sin(2 * np.pi * freq * 2 * t)
            
            # Envelope
            envelope = np.exp(-t * 2) * (1 - np.exp(-t * 10))
            
            # Modulation
            modulation = 1 + 0.2 * np.sin(2 * np.pi * 3 * t)
            
            sample = wave * envelope * modulation * 0.2
            arr[i] = [sample, sample]
        
        sound_array = (arr * 32767).astype(np.int16)
        sound = pygame.sndarray.make_sound(sound_array)
        
        self.sound_cache[cache_key] = sound
        return sound
    
    def generate_timer_tick(self) -> pygame.mixer.Sound:
        """Generate subtle timer tick sound"""
        cache_key = "tick"
        if cache_key in self.sound_cache:
            return self.sound_cache[cache_key]
        
        duration = 0.1
        frames = int(duration * self.sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            t = i / self.sample_rate
            
            # Short click sound
            wave = np.sin(2 * np.pi * 1000 * t)
            envelope = np.exp(-t * 50)
            
            sample = wave * envelope * 0.1
            arr[i] = [sample, sample]
        
        sound_array = (arr * 32767).astype(np.int16)
        sound = pygame.sndarray.make_sound(sound_array)
        
        self.sound_cache[cache_key] = sound
        return sound


class AudioManager:
    """Manages audio effects and ambient sounds"""
    
    def __init__(self):
        self.sound_generator = SoundGenerator()
        self.enabled = True
        self.volume = 0.5
        
        # Pre-generate common sounds
        self._preload_sounds()
    
    def _preload_sounds(self):
        """Preload commonly used sounds"""
        try:
            # Notification sounds for different sessions
            self.work_start_sound = self.sound_generator.generate_notification_sound(440, 0.8)  # A4
            self.break_start_sound = self.sound_generator.generate_notification_sound(523, 0.8)  # C5
            self.session_complete_sound = self.sound_generator.generate_notification_sound(659, 1.0)  # E5
            
            # System sounds
            self.startup_sound = self.sound_generator.generate_startup_sound()
            self.tick_sound = self.sound_generator.generate_timer_tick()
            
        except Exception as e:
            print(f"Error preloading sounds: {e}")
            self.enabled = False
    
    def set_enabled(self, enabled: bool):
        """Enable or disable audio effects"""
        self.enabled = enabled
    
    def set_volume(self, volume: float):
        """Set audio volume (0.0 to 1.0)"""
        self.volume = max(0.0, min(1.0, volume))
    
    def play_startup(self):
        """Play startup sound"""
        if self.enabled and hasattr(self, 'startup_sound'):
            self.startup_sound.set_volume(self.volume)
            self.startup_sound.play()
    
    def play_work_start(self):
        """Play work session start sound"""
        if self.enabled and hasattr(self, 'work_start_sound'):
            self.work_start_sound.set_volume(self.volume)
            self.work_start_sound.play()
    
    def play_break_start(self):
        """Play break start sound"""
        if self.enabled and hasattr(self, 'break_start_sound'):
            self.break_start_sound.set_volume(self.volume)
            self.break_start_sound.play()
    
    def play_session_complete(self):
        """Play session complete sound"""
        if self.enabled and hasattr(self, 'session_complete_sound'):
            self.session_complete_sound.set_volume(self.volume * 1.2)  # Slightly louder
            self.session_complete_sound.play()
    
    def play_tick(self):
        """Play timer tick sound"""
        if self.enabled and hasattr(self, 'tick_sound'):
            self.tick_sound.set_volume(self.volume * 0.3)  # Much quieter
            self.tick_sound.play()
    
    def stop_all(self):
        """Stop all playing sounds"""
        pygame.mixer.stop()


class AmbientSoundManager:
    """Manages ambient background sounds for focus"""
    
    def __init__(self):
        self.audio_manager = AudioManager()
        self.ambient_enabled = False
        self.ambient_volume = 0.3
        self.ambient_thread: Optional[threading.Thread] = None
        self.ambient_running = False
    
    def set_ambient_enabled(self, enabled: bool):
        """Enable or disable ambient sounds"""
        self.ambient_enabled = enabled
        if enabled:
            self.start_ambient()
        else:
            self.stop_ambient()
    
    def set_ambient_volume(self, volume: float):
        """Set ambient sound volume"""
        self.ambient_volume = max(0.0, min(1.0, volume))
    
    def start_ambient(self):
        """Start ambient background sounds"""
        if self.ambient_running or not self.ambient_enabled:
            return
        
        self.ambient_running = True
        self.ambient_thread = threading.Thread(target=self._ambient_loop, daemon=True)
        self.ambient_thread.start()
    
    def stop_ambient(self):
        """Stop ambient background sounds"""
        self.ambient_running = False
    
    def _ambient_loop(self):
        """Ambient sound loop"""
        while self.ambient_running:
            try:
                # Generate subtle ambient sound
                ambient_sound = self._generate_ambient_sound()
                if ambient_sound:
                    ambient_sound.set_volume(self.ambient_volume)
                    ambient_sound.play()
                
                # Wait before next ambient sound
                time.sleep(5.0)
                
            except Exception as e:
                print(f"Error in ambient loop: {e}")
                break
    
    def _generate_ambient_sound(self) -> Optional[pygame.mixer.Sound]:
        """Generate subtle ambient sound"""
        try:
            duration = 3.0
            frames = int(duration * 22050)
            arr = np.zeros((frames, 2))
            
            for i in range(frames):
                t = i / 22050
                
                # Very low frequency ambient tone
                wave = 0.1 * np.sin(2 * np.pi * 60 * t)  # 60 Hz
                wave += 0.05 * np.sin(2 * np.pi * 120 * t)  # 120 Hz
                
                # Slow modulation
                modulation = 1 + 0.1 * np.sin(2 * np.pi * 0.1 * t)
                
                # Gentle envelope
                envelope = 1.0
                fade_time = 0.5
                if t < fade_time:
                    envelope = t / fade_time
                elif t > duration - fade_time:
                    envelope = (duration - t) / fade_time
                
                sample = wave * envelope * modulation
                arr[i] = [sample, sample]
            
            sound_array = (arr * 32767).astype(np.int16)
            return pygame.sndarray.make_sound(sound_array)
            
        except Exception as e:
            print(f"Error generating ambient sound: {e}")
            return None
