"""
Main UI Window for J.A.R.V.I.S. Pomodoro Timer
Modern, premium interface with dark theme and glowing effects
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import Canvas
import math
from typing import Callable, Optional
from src.timer import PomodoroTimer, TimerState, SessionType
from src.settings import SettingsManager


class CircularProgress(Canvas):
    """Custom circular progress indicator with glow effects"""
    
    def __init__(self, parent, size=200, **kwargs):
        super().__init__(parent, width=size, height=size, highlightthickness=0, **kwargs)
        self.size = size
        self.center = size // 2
        self.radius = size // 2 - 20
        self.progress = 0.0
        self.glow_color = "#00D4FF"
        self.bg_color = "#2A2A2A"

        # Set background color for canvas
        self.configure(bg="#1A1A1A")
        self.draw_progress()
    
    def set_progress(self, progress: float, color: str = None):
        """Update progress (0.0 to 1.0)"""
        self.progress = max(0.0, min(1.0, progress))
        if color:
            self.glow_color = color
        self.draw_progress()
    
    def draw_progress(self):
        """Draw the circular progress with glow effect"""
        self.delete("all")
        
        # Background circle
        self.create_oval(
            self.center - self.radius, self.center - self.radius,
            self.center + self.radius, self.center + self.radius,
            outline=self.bg_color, width=8, fill=""
        )
        
        # Progress arc with glow effect
        if self.progress > 0:
            # Multiple arcs for glow effect
            for i in range(5):
                alpha = 1.0 - (i * 0.15)
                width = 8 + (i * 2)
                
                # Calculate arc extent
                extent = self.progress * 360
                
                self.create_arc(
                    self.center - self.radius - i, self.center - self.radius - i,
                    self.center + self.radius + i, self.center + self.radius + i,
                    start=90, extent=-extent,
                    outline=self._adjust_color_alpha(self.glow_color, alpha),
                    width=width, style="arc"
                )
    
    def _adjust_color_alpha(self, color: str, alpha: float) -> str:
        """Adjust color transparency for glow effect"""
        # Simple alpha adjustment by modifying brightness
        if color.startswith("#"):
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
            
            r = int(r * alpha)
            g = int(g * alpha)
            b = int(b * alpha)
            
            return f"#{r:02x}{g:02x}{b:02x}"
        return color


class ModernButton(ctk.CTkButton):
    """Custom button with J.A.R.V.I.S. styling"""
    
    def __init__(self, parent, **kwargs):
        # Default styling
        default_kwargs = {
            "corner_radius": 25,
            "border_width": 2,
            "border_color": "#00D4FF",
            "fg_color": "transparent",
            "hover_color": "#00D4FF",
            "text_color": "#00D4FF",
            "font": ("Segoe UI", 14, "bold")
        }
        default_kwargs.update(kwargs)
        super().__init__(parent, **default_kwargs)


class MainWindow:
    """Main application window with modern J.A.R.V.I.S. interface"""
    
    def __init__(self, timer: PomodoroTimer, settings_manager: SettingsManager):
        self.timer = timer
        self.settings = settings_manager
        self.root = None
        self.progress_widget = None
        self.time_label = None
        self.session_label = None
        self.start_button = None
        self.pause_button = None
        self.stop_button = None
        self.skip_button = None
        
        # Callbacks
        self.on_settings_click: Optional[Callable] = None
        
        self._setup_ui()
        self._setup_timer_callbacks()
    
    def _setup_ui(self):
        """Initialize the main UI"""
        # Configure CustomTkinter
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("J.A.R.V.I.S. Pomodoro Timer")
        self.root.geometry(f"{self.settings.settings.ui.window_width}x{self.settings.settings.ui.window_height}")
        self.root.configure(fg_color=self.settings.settings.ui.background_color)
        
        if self.settings.settings.ui.always_on_top:
            self.root.attributes("-topmost", True)
        
        # Main container
        main_frame = ctk.CTkFrame(self.root, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="J.A.R.V.I.S. POMODORO",
            font=("Segoe UI", 24, "bold"),
            text_color=self.settings.settings.ui.theme_color
        )
        title_label.pack(pady=(0, 20))
        
        # Progress circle container
        progress_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        progress_frame.pack(pady=20)
        
        # Circular progress
        self.progress_widget = CircularProgress(
            progress_frame,
            size=220
        )
        self.progress_widget.pack()

        # Time display (overlay on progress circle)
        self.time_label = ctk.CTkLabel(
            main_frame,
            text="25:00",
            font=("Segoe UI", 36, "bold"),
            text_color=self.settings.settings.ui.text_color
        )
        self.time_label.pack(pady=(10, 0))
        
        # Session info
        self.session_label = ctk.CTkLabel(
            main_frame,
            text="WORK SESSION",
            font=("Segoe UI", 16, "bold"),
            text_color=self.settings.settings.ui.accent_color
        )
        self.session_label.pack(pady=(10, 20))
        
        # Control buttons
        self._create_control_buttons(main_frame)
        
        # Settings button
        settings_button = ModernButton(
            main_frame,
            text="⚙ SETTINGS",
            width=120,
            command=self._on_settings_click
        )
        settings_button.pack(pady=(20, 0))
    
    def _create_control_buttons(self, parent):
        """Create timer control buttons"""
        button_frame = ctk.CTkFrame(parent, fg_color="transparent")
        button_frame.pack(pady=20)
        
        # Start button
        self.start_button = ModernButton(
            button_frame,
            text="▶ START",
            width=100,
            command=self._on_start_click,
            fg_color=self.settings.settings.ui.theme_color,
            hover_color=self.settings.settings.ui.secondary_color,
            text_color="#FFFFFF"
        )
        self.start_button.grid(row=0, column=0, padx=5)
        
        # Pause button
        self.pause_button = ModernButton(
            button_frame,
            text="⏸ PAUSE",
            width=100,
            command=self._on_pause_click
        )
        self.pause_button.grid(row=0, column=1, padx=5)
        
        # Stop button
        self.stop_button = ModernButton(
            button_frame,
            text="⏹ STOP",
            width=100,
            command=self._on_stop_click
        )
        self.stop_button.grid(row=0, column=2, padx=5)
        
        # Skip button
        self.skip_button = ModernButton(
            button_frame,
            text="⏭ SKIP",
            width=100,
            command=self._on_skip_click
        )
        self.skip_button.grid(row=1, column=0, columnspan=3, pady=(10, 0))
    
    def _setup_timer_callbacks(self):
        """Setup timer event callbacks"""
        self.timer.on_tick = self._on_timer_tick
        self.timer.on_state_change = self._on_timer_state_change
        self.timer.on_session_complete = self._on_session_complete
    
    def _on_timer_tick(self, time_remaining: int):
        """Handle timer tick updates"""
        if self.time_label:
            self.time_label.configure(text=self.timer.format_time(time_remaining))
        
        if self.progress_widget:
            progress = self.timer.get_progress()
            color = self._get_session_color()
            self.progress_widget.set_progress(progress, color)
    
    def _on_timer_state_change(self, state: TimerState):
        """Handle timer state changes"""
        self._update_button_states()
        self._update_session_display()
    
    def _on_session_complete(self, session_type: SessionType):
        """Handle session completion"""
        self._update_session_display()
    
    def _update_button_states(self):
        """Update button states based on timer state"""
        if not self.start_button:
            return
        
        state = self.timer.state
        
        if state == TimerState.STOPPED:
            self.start_button.configure(state="normal", text="▶ START")
            self.pause_button.configure(state="disabled")
            self.stop_button.configure(state="disabled")
        elif state == TimerState.RUNNING:
            self.start_button.configure(state="disabled")
            self.pause_button.configure(state="normal")
            self.stop_button.configure(state="normal")
        elif state == TimerState.PAUSED:
            self.start_button.configure(state="normal", text="▶ RESUME")
            self.pause_button.configure(state="disabled")
            self.stop_button.configure(state="normal")
    
    def _update_session_display(self):
        """Update session type display"""
        if not self.session_label:
            return
        
        session_type = self.timer.current_session
        session_count = self.timer.session_count
        
        if session_type == SessionType.WORK:
            text = f"WORK SESSION #{session_count + 1}"
        elif session_type == SessionType.SHORT_BREAK:
            text = "SHORT BREAK"
        else:  # LONG_BREAK
            text = "LONG BREAK"
        
        self.session_label.configure(text=text)
    
    def _get_session_color(self) -> str:
        """Get color for current session type"""
        if self.timer.current_session == SessionType.WORK:
            return self.settings.settings.ui.theme_color
        elif self.timer.current_session == SessionType.SHORT_BREAK:
            return "#00FF88"  # Green for breaks
        else:  # LONG_BREAK
            return "#FF8800"  # Orange for long breaks
    
    def _on_start_click(self):
        """Handle start button click"""
        self.timer.start()
    
    def _on_pause_click(self):
        """Handle pause button click"""
        self.timer.pause()
    
    def _on_stop_click(self):
        """Handle stop button click"""
        self.timer.stop()
    
    def _on_skip_click(self):
        """Handle skip button click"""
        self.timer.skip_session()
    
    def _on_settings_click(self):
        """Handle settings button click"""
        if self.on_settings_click:
            self.on_settings_click()
    
    def run(self):
        """Start the main event loop"""
        if self.root:
            self.root.mainloop()
    
    def destroy(self):
        """Clean up and destroy window"""
        if self.root:
            self.root.destroy()
