#!/usr/bin/env python3
"""
Simple Demo for J.A.R.V.I.S. Pomodoro Timer
Demonstrates core functionality without GUI components
"""

import sys
import os
import time

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.timer import PomodoroTimer, TimerSettings, SessionType
from src.settings import SettingsManager
from src.notifications.voice_notification import VoiceNotificationManager


def main():
    """Run a simple demonstration"""
    print("🤖 J.A.R.V.I.S. Pomodoro Timer - Simple Demo")
    print("=" * 50)
    print()
    
    # 1. Settings Demo
    print("⚙ Settings Management:")
    settings_manager = SettingsManager("demo_config.json")
    print(f"  ✓ Work duration: {settings_manager.settings.timer.work_duration // 60} minutes")
    print(f"  ✓ Theme color: {settings_manager.settings.ui.theme_color}")
    print(f"  ✓ Voice enabled: {settings_manager.settings.notifications.enable_voice}")
    print()
    
    # 2. Voice Demo
    print("🔊 Voice Notifications:")
    voice_manager = VoiceNotificationManager()
    if voice_manager.engine:
        print("  ✓ TTS engine initialized")
        print("  Speaking welcome message...")
        voice_manager.speak("Welcome to J.A.R.V.I.S. Pomodoro Timer", blocking=True)
    else:
        print("  ⚠ TTS engine not available")
    print()
    
    # 3. Timer Demo
    print("⏱ Timer Functionality:")
    
    # Create timer with short durations for demo
    settings = TimerSettings(
        work_duration=5,  # 5 seconds
        short_break_duration=3,  # 3 seconds
        long_break_duration=5,  # 5 seconds
        sessions_until_long_break=2
    )
    
    timer = PomodoroTimer(settings)
    
    # Setup callbacks
    def on_tick(time_remaining):
        print(f"    ⏰ {timer.format_time(time_remaining)} - {timer.current_session.value}")
    
    def on_session_complete(session_type):
        print(f"    ✅ {session_type.value} session complete!")
        if voice_manager.engine:
            if session_type == SessionType.WORK:
                voice_manager.speak("Work session complete. Time for a break.", blocking=False)
            else:
                voice_manager.speak("Break complete. Ready to focus again?", blocking=False)
    
    timer.on_tick = on_tick
    timer.on_session_complete = on_session_complete
    
    # Run a quick cycle
    print("  Starting work session (5 seconds)...")
    timer.start()
    time.sleep(6)
    
    print("  Starting break (3 seconds)...")
    timer.start()
    time.sleep(4)
    
    print("  ✓ Timer demo complete!")
    print()
    
    # 4. Features Summary
    print("🎯 Available Features:")
    print("  ✓ Core Pomodoro timer with work/break cycles")
    print("  ✓ Customizable timer durations and settings")
    print("  ✓ Voice notifications with TTS")
    print("  ✓ Settings persistence and management")
    print("  ✓ Session state tracking and callbacks")
    print()
    
    print("🚀 Full Application Features:")
    print("  🎨 Modern premium UI with dark theme")
    print("  🤖 J.A.R.V.I.S.-style visual notifications")
    print("  🔄 System tray integration")
    print("  ⚙️ Comprehensive settings panel")
    print("  🎵 Synthetic sound effects")
    print("  🔝 Always-on-top functionality")
    print()
    
    print("To start the full GUI application:")
    print("    python main.py")
    print()
    
    # Clean up
    if os.path.exists("demo_config.json"):
        os.remove("demo_config.json")
    
    print("Demo complete! 🎉")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"\nDemo failed: {e}")
        import traceback
        traceback.print_exc()
