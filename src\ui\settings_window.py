"""
Settings Window for J.A.R.V.I.S. Pomodoro Timer
Comprehensive settings interface for customization
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from src.settings import SettingsManager


class SettingsWindow:
    """Settings configuration window"""
    
    def __init__(self, settings_manager: SettingsManager, parent=None):
        self.settings_manager = settings_manager
        self.parent = parent
        self.window = None
        self.widgets = {}
        
        self._create_window()
        self._create_widgets()
        self._load_current_settings()
    
    def _create_window(self):
        """Create the settings window"""
        self.window = ctk.CTkToplevel(self.parent)
        self.window.title("J.A.R.V.I.S. Settings")
        self.window.geometry("600x700")
        self.window.configure(fg_color="#1A1A1A")
        
        # Make modal
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # Center the window
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (700 // 2)
        self.window.geometry(f"600x700+{x}+{y}")
    
    def _create_widgets(self):
        """Create all settings widgets"""
        # Main container with scrollable frame
        main_frame = ctk.CTkScrollableFrame(self.window, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="J.A.R.V.I.S. SETTINGS",
            font=("Segoe UI", 24, "bold"),
            text_color="#00D4FF"
        )
        title_label.pack(pady=(0, 30))
        
        # Timer Settings Section
        self._create_timer_settings(main_frame)
        
        # Notification Settings Section
        self._create_notification_settings(main_frame)
        
        # UI Settings Section
        self._create_ui_settings(main_frame)
        
        # Action buttons
        self._create_action_buttons(main_frame)
    
    def _create_timer_settings(self, parent):
        """Create timer configuration section"""
        # Section header
        timer_frame = ctk.CTkFrame(parent, fg_color="#2A2A2A")
        timer_frame.pack(fill="x", pady=(0, 20))
        
        header = ctk.CTkLabel(
            timer_frame,
            text="⏱ TIMER SETTINGS",
            font=("Segoe UI", 18, "bold"),
            text_color="#00D4FF"
        )
        header.pack(pady=(15, 10))
        
        # Work duration
        work_frame = ctk.CTkFrame(timer_frame, fg_color="transparent")
        work_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(work_frame, text="Work Duration (minutes):", 
                    font=("Segoe UI", 12)).pack(side="left")
        self.widgets['work_duration'] = ctk.CTkEntry(work_frame, width=80)
        self.widgets['work_duration'].pack(side="right")
        
        # Short break duration
        short_break_frame = ctk.CTkFrame(timer_frame, fg_color="transparent")
        short_break_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(short_break_frame, text="Short Break Duration (minutes):", 
                    font=("Segoe UI", 12)).pack(side="left")
        self.widgets['short_break_duration'] = ctk.CTkEntry(short_break_frame, width=80)
        self.widgets['short_break_duration'].pack(side="right")
        
        # Long break duration
        long_break_frame = ctk.CTkFrame(timer_frame, fg_color="transparent")
        long_break_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(long_break_frame, text="Long Break Duration (minutes):", 
                    font=("Segoe UI", 12)).pack(side="left")
        self.widgets['long_break_duration'] = ctk.CTkEntry(long_break_frame, width=80)
        self.widgets['long_break_duration'].pack(side="right")
        
        # Sessions until long break
        sessions_frame = ctk.CTkFrame(timer_frame, fg_color="transparent")
        sessions_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(sessions_frame, text="Sessions until Long Break:", 
                    font=("Segoe UI", 12)).pack(side="left")
        self.widgets['sessions_until_long_break'] = ctk.CTkEntry(sessions_frame, width=80)
        self.widgets['sessions_until_long_break'].pack(side="right")
        
        # Auto-start options
        auto_frame = ctk.CTkFrame(timer_frame, fg_color="transparent")
        auto_frame.pack(fill="x", padx=20, pady=(10, 15))
        
        self.widgets['auto_start_breaks'] = ctk.CTkCheckBox(
            auto_frame, text="Auto-start breaks", font=("Segoe UI", 12)
        )
        self.widgets['auto_start_breaks'].pack(side="left", padx=(0, 20))
        
        self.widgets['auto_start_work'] = ctk.CTkCheckBox(
            auto_frame, text="Auto-start work", font=("Segoe UI", 12)
        )
        self.widgets['auto_start_work'].pack(side="left")
    
    def _create_notification_settings(self, parent):
        """Create notification configuration section"""
        notif_frame = ctk.CTkFrame(parent, fg_color="#2A2A2A")
        notif_frame.pack(fill="x", pady=(0, 20))
        
        header = ctk.CTkLabel(
            notif_frame,
            text="🔊 NOTIFICATION SETTINGS",
            font=("Segoe UI", 18, "bold"),
            text_color="#00D4FF"
        )
        header.pack(pady=(15, 10))
        
        # Enable options
        enable_frame = ctk.CTkFrame(notif_frame, fg_color="transparent")
        enable_frame.pack(fill="x", padx=20, pady=5)
        
        self.widgets['enable_voice'] = ctk.CTkCheckBox(
            enable_frame, text="Enable Voice Notifications", font=("Segoe UI", 12)
        )
        self.widgets['enable_voice'].pack(side="left", padx=(0, 20))
        
        self.widgets['enable_visual'] = ctk.CTkCheckBox(
            enable_frame, text="Enable Visual Notifications", font=("Segoe UI", 12)
        )
        self.widgets['enable_visual'].pack(side="left")
        
        # Voice settings
        voice_frame = ctk.CTkFrame(notif_frame, fg_color="transparent")
        voice_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(voice_frame, text="Voice Volume:", 
                    font=("Segoe UI", 12)).pack(side="left")
        self.widgets['voice_volume'] = ctk.CTkSlider(voice_frame, from_=0.0, to=1.0, width=150)
        self.widgets['voice_volume'].pack(side="right")
        
        # Voice rate
        rate_frame = ctk.CTkFrame(notif_frame, fg_color="transparent")
        rate_frame.pack(fill="x", padx=20, pady=(5, 15))
        
        ctk.CTkLabel(rate_frame, text="Voice Rate:", 
                    font=("Segoe UI", 12)).pack(side="left")
        self.widgets['voice_rate'] = ctk.CTkSlider(rate_frame, from_=100, to=300, width=150)
        self.widgets['voice_rate'].pack(side="right")
    
    def _create_ui_settings(self, parent):
        """Create UI configuration section"""
        ui_frame = ctk.CTkFrame(parent, fg_color="#2A2A2A")
        ui_frame.pack(fill="x", pady=(0, 20))
        
        header = ctk.CTkLabel(
            ui_frame,
            text="🎨 UI SETTINGS",
            font=("Segoe UI", 18, "bold"),
            text_color="#00D4FF"
        )
        header.pack(pady=(15, 10))
        
        # Theme color
        color_frame = ctk.CTkFrame(ui_frame, fg_color="transparent")
        color_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(color_frame, text="Theme Color:", 
                    font=("Segoe UI", 12)).pack(side="left")
        self.widgets['theme_color'] = ctk.CTkEntry(color_frame, width=100)
        self.widgets['theme_color'].pack(side="right")
        
        # UI options
        ui_options_frame = ctk.CTkFrame(ui_frame, fg_color="transparent")
        ui_options_frame.pack(fill="x", padx=20, pady=5)
        
        self.widgets['always_on_top'] = ctk.CTkCheckBox(
            ui_options_frame, text="Always on Top", font=("Segoe UI", 12)
        )
        self.widgets['always_on_top'].pack(side="left", padx=(0, 20))
        
        self.widgets['minimize_to_tray'] = ctk.CTkCheckBox(
            ui_options_frame, text="Minimize to Tray", font=("Segoe UI", 12)
        )
        self.widgets['minimize_to_tray'].pack(side="left")
        
        # Animation settings
        anim_frame = ctk.CTkFrame(ui_frame, fg_color="transparent")
        anim_frame.pack(fill="x", padx=20, pady=(5, 15))
        
        self.widgets['enable_animations'] = ctk.CTkCheckBox(
            anim_frame, text="Enable Animations", font=("Segoe UI", 12)
        )
        self.widgets['enable_animations'].pack(side="left")
    
    def _create_action_buttons(self, parent):
        """Create action buttons"""
        button_frame = ctk.CTkFrame(parent, fg_color="transparent")
        button_frame.pack(fill="x", pady=20)
        
        # Save button
        save_button = ctk.CTkButton(
            button_frame,
            text="💾 SAVE SETTINGS",
            command=self._save_settings,
            fg_color="#00D4FF",
            hover_color="#0099CC",
            font=("Segoe UI", 14, "bold"),
            width=150
        )
        save_button.pack(side="left", padx=(0, 10))
        
        # Reset button
        reset_button = ctk.CTkButton(
            button_frame,
            text="🔄 RESET TO DEFAULTS",
            command=self._reset_settings,
            fg_color="#FF4444",
            hover_color="#CC3333",
            font=("Segoe UI", 14, "bold"),
            width=150
        )
        reset_button.pack(side="left", padx=10)
        
        # Cancel button
        cancel_button = ctk.CTkButton(
            button_frame,
            text="❌ CANCEL",
            command=self._cancel,
            fg_color="transparent",
            border_width=2,
            border_color="#666666",
            font=("Segoe UI", 14, "bold"),
            width=100
        )
        cancel_button.pack(side="right")
    
    def _load_current_settings(self):
        """Load current settings into widgets"""
        settings = self.settings_manager.settings
        
        # Timer settings
        self.widgets['work_duration'].insert(0, str(settings.timer.work_duration // 60))
        self.widgets['short_break_duration'].insert(0, str(settings.timer.short_break_duration // 60))
        self.widgets['long_break_duration'].insert(0, str(settings.timer.long_break_duration // 60))
        self.widgets['sessions_until_long_break'].insert(0, str(settings.timer.sessions_until_long_break))
        
        if settings.timer.auto_start_breaks:
            self.widgets['auto_start_breaks'].select()
        if settings.timer.auto_start_work:
            self.widgets['auto_start_work'].select()
        
        # Notification settings
        if settings.notifications.enable_voice:
            self.widgets['enable_voice'].select()
        if settings.notifications.enable_visual:
            self.widgets['enable_visual'].select()
        
        self.widgets['voice_volume'].set(settings.notifications.voice_volume)
        self.widgets['voice_rate'].set(settings.notifications.voice_rate)
        
        # UI settings
        self.widgets['theme_color'].insert(0, settings.ui.theme_color)
        
        if settings.ui.always_on_top:
            self.widgets['always_on_top'].select()
        if settings.ui.minimize_to_tray:
            self.widgets['minimize_to_tray'].select()
        if settings.ui.enable_animations:
            self.widgets['enable_animations'].select()
    
    def _save_settings(self):
        """Save settings and close window"""
        try:
            # Timer settings
            self.settings_manager.update_timer_settings(
                work_duration=int(self.widgets['work_duration'].get()) * 60,
                short_break_duration=int(self.widgets['short_break_duration'].get()) * 60,
                long_break_duration=int(self.widgets['long_break_duration'].get()) * 60,
                sessions_until_long_break=int(self.widgets['sessions_until_long_break'].get()),
                auto_start_breaks=bool(self.widgets['auto_start_breaks'].get()),
                auto_start_work=bool(self.widgets['auto_start_work'].get())
            )
            
            # Notification settings
            self.settings_manager.update_notification_settings(
                enable_voice=bool(self.widgets['enable_voice'].get()),
                enable_visual=bool(self.widgets['enable_visual'].get()),
                voice_volume=self.widgets['voice_volume'].get(),
                voice_rate=int(self.widgets['voice_rate'].get())
            )
            
            # UI settings
            self.settings_manager.update_ui_settings(
                theme_color=self.widgets['theme_color'].get(),
                always_on_top=bool(self.widgets['always_on_top'].get()),
                minimize_to_tray=bool(self.widgets['minimize_to_tray'].get()),
                enable_animations=bool(self.widgets['enable_animations'].get())
            )
            
            messagebox.showinfo("Settings Saved", "Settings have been saved successfully!")
            self.window.destroy()
            
        except ValueError as e:
            messagebox.showerror("Invalid Input", f"Please check your input values: {e}")
    
    def _reset_settings(self):
        """Reset settings to defaults"""
        if messagebox.askyesno("Reset Settings", "Are you sure you want to reset all settings to defaults?"):
            self.settings_manager.reset_to_defaults()
            messagebox.showinfo("Settings Reset", "Settings have been reset to defaults!")
            self.window.destroy()
    
    def _cancel(self):
        """Cancel and close window"""
        self.window.destroy()
