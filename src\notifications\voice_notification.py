"""
Voice Notification System for J.A.R.V.I.S. Pomodoro Timer
Handles text-to-speech notifications with customizable messages
"""

import pyttsx3
import threading
import time
from typing import Optional, Dict, Any
from src.timer import SessionType


class VoiceNotificationManager:
    """Manages voice notifications using text-to-speech"""
    
    def __init__(self):
        self.engine: Optional[pyttsx3.Engine] = None
        self.is_speaking = False
        self.speech_thread: Optional[threading.Thread] = None
        
        # Default settings
        self.volume = 0.8
        self.rate = 200
        self.voice_id = None
        
        self._initialize_engine()
    
    def _initialize_engine(self):
        """Initialize the TTS engine"""
        try:
            self.engine = pyttsx3.init()
            
            # Set default properties
            self.engine.setProperty('rate', self.rate)
            self.engine.setProperty('volume', self.volume)
            
            # Get available voices
            voices = self.engine.getProperty('voices')
            if voices:
                # Try to find a suitable voice (prefer female for J.A.R.V.I.S. feel)
                for voice in voices:
                    if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                        self.voice_id = voice.id
                        break
                
                # If no female voice found, use the first available
                if not self.voice_id and voices:
                    self.voice_id = voices[0].id
                
                if self.voice_id:
                    self.engine.setProperty('voice', self.voice_id)
            
        except Exception as e:
            print(f"Error initializing TTS engine: {e}")
            self.engine = None
    
    def update_settings(self, volume: float = None, rate: int = None, voice_id: str = None):
        """Update voice settings"""
        if not self.engine:
            return
        
        try:
            if volume is not None:
                self.volume = max(0.0, min(1.0, volume))
                self.engine.setProperty('volume', self.volume)
            
            if rate is not None:
                self.rate = max(100, min(300, rate))
                self.engine.setProperty('rate', self.rate)
            
            if voice_id is not None:
                self.voice_id = voice_id
                self.engine.setProperty('voice', self.voice_id)
                
        except Exception as e:
            print(f"Error updating voice settings: {e}")
    
    def speak(self, text: str, blocking: bool = False):
        """Speak the given text"""
        if not self.engine or not text.strip():
            return
        
        if self.is_speaking:
            self.stop_speaking()
        
        if blocking:
            self._speak_blocking(text)
        else:
            self._speak_async(text)
    
    def _speak_blocking(self, text: str):
        """Speak text in blocking mode"""
        try:
            self.is_speaking = True
            self.engine.say(text)
            self.engine.runAndWait()
        except Exception as e:
            print(f"Error in blocking speech: {e}")
        finally:
            self.is_speaking = False
    
    def _speak_async(self, text: str):
        """Speak text asynchronously"""
        if self.speech_thread and self.speech_thread.is_alive():
            return
        
        self.speech_thread = threading.Thread(
            target=self._speak_blocking,
            args=(text,),
            daemon=True
        )
        self.speech_thread.start()
    
    def stop_speaking(self):
        """Stop current speech"""
        if self.engine and self.is_speaking:
            try:
                self.engine.stop()
            except Exception as e:
                print(f"Error stopping speech: {e}")
        
        self.is_speaking = False
    
    def get_available_voices(self) -> list:
        """Get list of available voices"""
        if not self.engine:
            return []
        
        try:
            voices = self.engine.getProperty('voices')
            return [{'id': voice.id, 'name': voice.name} for voice in voices] if voices else []
        except Exception as e:
            print(f"Error getting voices: {e}")
            return []
    
    def test_voice(self, text: str = "This is a test of the voice notification system."):
        """Test the current voice settings"""
        self.speak(text, blocking=False)


class SessionNotificationManager:
    """Manages session-specific voice notifications"""
    
    def __init__(self, voice_manager: VoiceNotificationManager):
        self.voice_manager = voice_manager
        self.enabled = True
        
        # Default messages
        self.messages = {
            'work_start': "Time to focus. Let's get to work.",
            'work_complete': "Great work! Time for a break.",
            'short_break_start': "Break time. Relax and recharge.",
            'short_break_complete': "Break's over. Ready to focus again?",
            'long_break_start': "You've earned a long break. Take your time.",
            'long_break_complete': "Refreshed and ready. Let's continue the great work."
        }
    
    def update_messages(self, messages: Dict[str, str]):
        """Update notification messages"""
        self.messages.update(messages)
    
    def set_enabled(self, enabled: bool):
        """Enable or disable voice notifications"""
        self.enabled = enabled
    
    def notify_session_start(self, session_type: SessionType):
        """Notify about session start"""
        if not self.enabled:
            return
        
        message_key = self._get_message_key(session_type, is_start=True)
        message = self.messages.get(message_key, "Session starting.")
        
        self.voice_manager.speak(message)
    
    def notify_session_complete(self, session_type: SessionType):
        """Notify about session completion"""
        if not self.enabled:
            return
        
        message_key = self._get_message_key(session_type, is_start=False)
        message = self.messages.get(message_key, "Session complete.")
        
        self.voice_manager.speak(message)
    
    def _get_message_key(self, session_type: SessionType, is_start: bool) -> str:
        """Get message key for session type and state"""
        if session_type == SessionType.WORK:
            return 'work_start' if is_start else 'work_complete'
        elif session_type == SessionType.SHORT_BREAK:
            return 'short_break_start' if is_start else 'short_break_complete'
        elif session_type == SessionType.LONG_BREAK:
            return 'long_break_start' if is_start else 'long_break_complete'
        
        return 'work_start' if is_start else 'work_complete'
    
    def notify_custom(self, message: str):
        """Send custom voice notification"""
        if self.enabled and message.strip():
            self.voice_manager.speak(message)


class NotificationCoordinator:
    """Coordinates voice and visual notifications"""
    
    def __init__(self, voice_manager: VoiceNotificationManager, visual_manager=None):
        self.voice_manager = voice_manager
        self.visual_manager = visual_manager
        self.session_notifier = SessionNotificationManager(voice_manager)
        
        # Settings
        self.voice_enabled = True
        self.visual_enabled = True
    
    def update_settings(self, voice_enabled: bool = None, visual_enabled: bool = None):
        """Update notification settings"""
        if voice_enabled is not None:
            self.voice_enabled = voice_enabled
            self.session_notifier.set_enabled(voice_enabled)
        
        if visual_enabled is not None:
            self.visual_enabled = visual_enabled
    
    def notify_session_transition(self, session_type: SessionType, is_starting: bool, message: str = None):
        """Send coordinated notification for session transition"""
        # Determine message
        if message is None:
            if is_starting:
                self.session_notifier.notify_session_start(session_type)
            else:
                self.session_notifier.notify_session_complete(session_type)
        else:
            if self.voice_enabled:
                self.voice_manager.speak(message)
        
        # Visual notification
        if self.visual_enabled and self.visual_manager:
            display_message = message or self._get_display_message(session_type, is_starting)
            color = self._get_session_color(session_type)
            self.visual_manager.show_notification(display_message, duration=3.0, color=color)
    
    def _get_display_message(self, session_type: SessionType, is_starting: bool) -> str:
        """Get display message for visual notification"""
        if session_type == SessionType.WORK:
            return "FOCUS TIME" if is_starting else "WORK COMPLETE"
        elif session_type == SessionType.SHORT_BREAK:
            return "SHORT BREAK" if is_starting else "BREAK OVER"
        elif session_type == SessionType.LONG_BREAK:
            return "LONG BREAK" if is_starting else "BREAK COMPLETE"
        
        return "SESSION CHANGE"
    
    def _get_session_color(self, session_type: SessionType) -> str:
        """Get color for session type"""
        if session_type == SessionType.WORK:
            return "#00D4FF"  # Blue for work
        elif session_type == SessionType.SHORT_BREAK:
            return "#00FF88"  # Green for short break
        elif session_type == SessionType.LONG_BREAK:
            return "#FF8800"  # Orange for long break
        
        return "#00D4FF"
