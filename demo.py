#!/usr/bin/env python3
"""
Demo Script for J.A.R.V.I.S. Pomodoro Timer
Showcases key features and functionality
"""

import sys
import os
import time
import threading

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.timer import PomodoroTimer, TimerSettings, SessionType
from src.notifications.visual_notification import NotificationManager
from src.notifications.voice_notification import VoiceNotificationManager, NotificationCoordinator


def demo_visual_notifications():
    """Demonstrate J.A.R.V.I.S.-style visual notifications"""
    print("🎨 Demonstrating J.A.R.V.I.S. Visual Notifications...")

    try:
        visual_manager = NotificationManager()

        # Show different types of notifications
        notifications = [
            ("FOCUS TIME", "#00D4FF", "Work session notification"),
            ("SHORT BREAK", "#00FF88", "Break notification"),
            ("LONG BREAK", "#FF8800", "Long break notification"),
            ("SESSION COMPLETE", "#FFD700", "Completion notification")
        ]

        for message, color, description in notifications:
            print(f"  Showing {description}...")
            try:
                visual_manager.show_notification(message, duration=1.0, color=color)
                time.sleep(2)
            except Exception as e:
                print(f"    Note: Visual notification skipped ({e})")

        print("  Visual notifications demo complete!")

    except Exception as e:
        print(f"  Visual notifications not available in this environment: {e}")
        print("  (Visual notifications work best in the full GUI application)")


def demo_voice_notifications():
    """Demonstrate voice notification system"""
    print("🔊 Demonstrating Voice Notifications...")
    
    voice_manager = VoiceNotificationManager()
    
    if not voice_manager.engine:
        print("  TTS engine not available - skipping voice demo")
        return
    
    messages = [
        "Welcome to J.A.R.V.I.S. Pomodoro Timer",
        "Time to focus. Let's get to work.",
        "Great work! Time for a break.",
        "Break's over. Ready to focus again?"
    ]
    
    for message in messages:
        print(f"  Speaking: '{message}'")
        voice_manager.speak(message, blocking=True)
        time.sleep(0.5)
    
    print("  Voice notifications demo complete!")


def demo_timer_functionality():
    """Demonstrate core timer functionality"""
    print("⏱ Demonstrating Timer Functionality...")
    
    # Create timer with very short durations for demo
    settings = TimerSettings(
        work_duration=5,  # 5 seconds
        short_break_duration=3,  # 3 seconds
        long_break_duration=5,  # 5 seconds
        sessions_until_long_break=2
    )
    
    timer = PomodoroTimer(settings)
    
    # Setup callbacks for demo
    def on_tick(time_remaining):
        print(f"    ⏰ {timer.format_time(time_remaining)} - {timer.current_session.value}")
    
    def on_state_change(state):
        print(f"    🔄 State: {state.value}")
    
    def on_session_complete(session_type):
        print(f"    ✅ Completed: {session_type.value}")
    
    timer.on_tick = on_tick
    timer.on_state_change = on_state_change
    timer.on_session_complete = on_session_complete
    
    print("  Starting work session...")
    timer.start()
    time.sleep(6)  # Let work session complete
    
    print("  Starting break...")
    timer.start()
    time.sleep(4)  # Let break complete
    
    print("  Starting second work session...")
    timer.start()
    time.sleep(6)  # Let work session complete
    
    print("  Starting long break...")
    timer.start()
    time.sleep(3)
    
    print("  Stopping timer...")
    timer.stop()
    
    print("  Timer functionality demo complete!")


def demo_coordinated_notifications():
    """Demonstrate coordinated voice and visual notifications"""
    print("🎭 Demonstrating Coordinated Notifications...")

    try:
        visual_manager = NotificationManager()
        voice_manager = VoiceNotificationManager()
        coordinator = NotificationCoordinator(voice_manager, visual_manager)

        # For demo, prefer voice over visual to avoid threading issues
        if voice_manager.engine:
            coordinator.update_settings(voice_enabled=True, visual_enabled=False)
            print("  Using voice notifications for demo")
        else:
            print("  TTS engine not available - skipping coordinated demo")
            return

        # Demo different session transitions
        transitions = [
            (SessionType.WORK, True, "Starting work session"),
            (SessionType.WORK, False, "Work session complete"),
            (SessionType.SHORT_BREAK, True, "Starting short break"),
            (SessionType.SHORT_BREAK, False, "Break complete")
        ]

        for session_type, is_starting, description in transitions:
            print(f"  {description}...")
            coordinator.notify_session_transition(session_type, is_starting)
            time.sleep(3)

        print("  Coordinated notifications demo complete!")

    except Exception as e:
        print(f"  Coordinated notifications demo failed: {e}")
        print("  (Full coordination works best in the GUI application)")


def demo_settings_system():
    """Demonstrate settings management"""
    print("⚙ Demonstrating Settings System...")
    
    from src.settings import SettingsManager
    
    # Create settings manager with demo config
    settings_manager = SettingsManager("demo_config.json")
    
    print("  Default settings loaded")
    print(f"    Work duration: {settings_manager.settings.timer.work_duration // 60} minutes")
    print(f"    Theme color: {settings_manager.settings.ui.theme_color}")
    print(f"    Voice enabled: {settings_manager.settings.notifications.enable_voice}")
    
    # Update some settings
    print("  Updating settings...")
    settings_manager.update_timer_settings(work_duration=30*60)
    settings_manager.update_ui_settings(theme_color="#FF0000")
    settings_manager.update_notification_settings(enable_voice=False)
    
    print("  Settings updated:")
    print(f"    Work duration: {settings_manager.settings.timer.work_duration // 60} minutes")
    print(f"    Theme color: {settings_manager.settings.ui.theme_color}")
    print(f"    Voice enabled: {settings_manager.settings.notifications.enable_voice}")
    
    # Clean up demo config
    if os.path.exists("demo_config.json"):
        os.remove("demo_config.json")
    
    print("  Settings system demo complete!")


def main():
    """Run the complete demo"""
    print("🤖 J.A.R.V.I.S. Pomodoro Timer - Feature Demo")
    print("=" * 60)
    print()
    
    try:
        # Run demos in sequence
        demo_settings_system()
        print()
        
        demo_timer_functionality()
        print()
        
        demo_voice_notifications()
        print()
        
        demo_visual_notifications()
        print()
        
        demo_coordinated_notifications()
        print()
        
        print("=" * 60)
        print("🎉 Demo completed successfully!")
        print()
        print("To start the full application, run:")
        print("    python main.py")
        print()
        print("Features demonstrated:")
        print("  ✅ Core timer functionality with work/break cycles")
        print("  ✅ Settings management and persistence")
        print("  ✅ Voice notifications with TTS")
        print("  ✅ J.A.R.V.I.S.-style visual notifications")
        print("  ✅ Coordinated notification system")
        print()
        print("Additional features in the full app:")
        print("  🎯 Modern premium UI with CustomTkinter")
        print("  🔄 System tray integration")
        print("  ⚙️ Comprehensive settings panel")
        print("  🎨 Smooth animations and effects")
        print("  🔊 Synthetic J.A.R.V.I.S. sound effects")
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
