import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import math
import pyttsx3
import json
import os
from datetime import datetime, timedelta
import pygame
import sys

class JARVISPomodoroTimer:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_audio()
        self.load_settings()
        self.setup_ui()
        self.setup_bindings()
        
        # Timer thread
        self.timer_thread = None
        self.is_running = False
        self.is_paused = False
        self.current_session = "work"  # work, short_break, long_break
        self.session_count = 0
        
    def setup_window(self):
        self.root.title("JARVIS Pomodoro Timer")
        self.root.geometry("800x600")
        self.root.configure(bg="#0a0a0a")
        self.root.resizable(True, True)
        
        # Make window stay on top when needed
        self.root.attributes('-topmost', False)
        
        # Custom style
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.configure_styles()
        
    def configure_styles(self):
        # Configure custom styles for JARVIS theme
        self.style.configure('JARVIS.TFrame', background='#0a0a0a')
        self.style.configure('JARVIS.TLabel', 
                           background='#0a0a0a', 
                           foreground='#00d4ff',
                           font=('Orbitron', 12))
        self.style.configure('JARVIS.Title.TLabel',
                           background='#0a0a0a',
                           foreground='#00d4ff',
                           font=('Orbitron', 24, 'bold'))
        self.style.configure('JARVIS.Time.TLabel',
                           background='#0a0a0a',
                           foreground='#00d4ff',
                           font=('Orbitron', 48, 'bold'))
        
    def setup_variables(self):
        # Timer settings (in minutes)
        self.work_duration = tk.IntVar(value=25)
        self.short_break_duration = tk.IntVar(value=5)
        self.long_break_duration = tk.IntVar(value=15)
        
        # Audio settings
        self.voice_enabled = tk.BooleanVar(value=True)
        self.visual_enabled = tk.BooleanVar(value=True)
        
        # Voice messages
        self.work_message = tk.StringVar(value="Time to focus. Let's get to work.")
        self.short_break_message = tk.StringVar(value="Take a short break. You've earned it.")
        self.long_break_message = tk.StringVar(value="Time for a long break. Recharge your energy.")
        
        # Theme color
        self.theme_color = tk.StringVar(value="#00d4ff")
        
        # Current timer state
        self.time_left = 0
        self.total_time = 0
        
    def setup_audio(self):
        try:
            pygame.mixer.init()
            self.tts_engine = pyttsx3.init()
            
            # Configure TTS voice
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Try to find a suitable voice (prefer male voice for JARVIS feel)
                for voice in voices:
                    if 'male' in voice.name.lower() or 'david' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
                        
            self.tts_engine.setProperty('rate', 150)
            self.tts_engine.setProperty('volume', 0.9)
            
        except Exception as e:
            print(f"Audio initialization error: {e}")
            self.voice_enabled.set(False)
    
    def setup_ui(self):
        # Main container
        main_frame = ttk.Frame(self.root, style='JARVIS.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(main_frame, text="J.A.R.V.I.S. POMODORO", 
                               style='JARVIS.Title.TLabel')
        title_label.pack(pady=(0, 30))
        
        # Timer display frame
        timer_frame = ttk.Frame(main_frame, style='JARVIS.TFrame')
        timer_frame.pack(pady=20)
        
        # Create circular timer display
        self.create_circular_timer(timer_frame)
        
        # Session info
        self.session_label = ttk.Label(main_frame, text="WORK SESSION", 
                                      style='JARVIS.TLabel',
                                      font=('Orbitron', 16, 'bold'))
        self.session_label.pack(pady=(10, 0))
        
        # Controls frame
        controls_frame = ttk.Frame(main_frame, style='JARVIS.TFrame')
        controls_frame.pack(pady=30)
        
        # Control buttons
        self.create_control_buttons(controls_frame)
        
        # Settings panel
        self.create_settings_panel(main_frame)
        
    def create_circular_timer(self, parent):
        # Canvas for circular timer
        self.canvas = tk.Canvas(parent, width=300, height=300, 
                               bg='#0a0a0a', highlightthickness=0)
        self.canvas.pack()
        
        # Time display
        self.time_label = ttk.Label(parent, text="25:00", 
                                   style='JARVIS.Time.TLabel')
        self.time_label.pack()
        
        # Initialize timer display
        self.update_timer_display()
        
    def create_control_buttons(self, parent):
        button_style = {
            'font': ('Orbitron', 12, 'bold'),
            'bg': '#1a1a1a',
            'fg': '#00d4ff',
            'activebackground': '#00d4ff',
            'activeforeground': '#0a0a0a',
            'relief': 'flat',
            'padx': 20,
            'pady': 10,
            'cursor': 'hand2'
        }
        
        buttons_frame = tk.Frame(parent, bg='#0a0a0a')
        buttons_frame.pack()
        
        self.start_btn = tk.Button(buttons_frame, text="START", 
                                  command=self.start_timer, **button_style)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.pause_btn = tk.Button(buttons_frame, text="PAUSE", 
                                  command=self.pause_timer, **button_style)
        self.pause_btn.pack(side=tk.LEFT, padx=5)
        
        self.reset_btn = tk.Button(buttons_frame, text="RESET", 
                                  command=self.reset_timer, **button_style)
        self.reset_btn.pack(side=tk.LEFT, padx=5)
        
        self.skip_btn = tk.Button(buttons_frame, text="SKIP", 
                                 command=self.skip_session, **button_style)
        self.skip_btn.pack(side=tk.LEFT, padx=5)
        
    def create_settings_panel(self, parent):
        settings_frame = ttk.LabelFrame(parent, text="SETTINGS", 
                                       style='JARVIS.TFrame',
                                       labelanchor='n')
        settings_frame.pack(fill=tk.X, pady=20)
        
        # Duration settings
        duration_frame = tk.Frame(settings_frame, bg='#0a0a0a')
        duration_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Work duration
        tk.Label(duration_frame, text="Work (min):", 
                bg='#0a0a0a', fg='#00d4ff', 
                font=('Orbitron', 10)).grid(row=0, column=0, sticky='w', padx=5)
        tk.Spinbox(duration_frame, from_=1, to=120, width=10,
                  textvariable=self.work_duration,
                  bg='#1a1a1a', fg='#00d4ff',
                  buttonbackground='#00d4ff').grid(row=0, column=1, padx=5)
        
        # Short break duration
        tk.Label(duration_frame, text="Short Break (min):", 
                bg='#0a0a0a', fg='#00d4ff',
                font=('Orbitron', 10)).grid(row=0, column=2, sticky='w', padx=5)
        tk.Spinbox(duration_frame, from_=1, to=60, width=10,
                  textvariable=self.short_break_duration,
                  bg='#1a1a1a', fg='#00d4ff',
                  buttonbackground='#00d4ff').grid(row=0, column=3, padx=5)
        
        # Long break duration
        tk.Label(duration_frame, text="Long Break (min):", 
                bg='#0a0a0a', fg='#00d4ff',
                font=('Orbitron', 10)).grid(row=1, column=0, sticky='w', padx=5)
        tk.Spinbox(duration_frame, from_=1, to=120, width=10,
                  textvariable=self.long_break_duration,
                  bg='#1a1a1a', fg='#00d4ff',
                  buttonbackground='#00d4ff').grid(row=1, column=1, padx=5)
        
        # Audio/Visual settings
        options_frame = tk.Frame(settings_frame, bg='#0a0a0a')
        options_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Checkbutton(options_frame, text="Voice Notifications", 
                      variable=self.voice_enabled,
                      bg='#0a0a0a', fg='#00d4ff', 
                      selectcolor='#1a1a1a',
                      font=('Orbitron', 10)).pack(side=tk.LEFT, padx=10)
        
        tk.Checkbutton(options_frame, text="Visual Effects", 
                      variable=self.visual_enabled,
                      bg='#0a0a0a', fg='#00d4ff',
                      selectcolor='#1a1a1a',
                      font=('Orbitron', 10)).pack(side=tk.LEFT, padx=10)
        
        # Voice message customization
        messages_frame = tk.Frame(settings_frame, bg='#0a0a0a')
        messages_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Collapsible messages section
        self.create_message_settings(messages_frame)
        
    def create_message_settings(self, parent):
        # Messages button
        msg_btn = tk.Button(parent, text="CUSTOMIZE VOICE MESSAGES", 
                           command=self.open_message_window,
                           font=('Orbitron', 10, 'bold'),
                           bg='#1a1a1a', fg='#00d4ff',
                           relief='flat', padx=15, pady=5)
        msg_btn.pack(pady=5)
        
    def open_message_window(self):
        msg_window = tk.Toplevel(self.root)
        msg_window.title("Voice Messages")
        msg_window.geometry("500x400")
        msg_window.configure(bg='#0a0a0a')
        msg_window.transient(self.root)
        
        # Work message
        tk.Label(msg_window, text="Work Session Message:", 
                bg='#0a0a0a', fg='#00d4ff',
                font=('Orbitron', 12, 'bold')).pack(anchor='w', padx=10, pady=(10,5))
        tk.Text(msg_window, height=3, width=60,
               bg='#1a1a1a', fg='#00d4ff',
               font=('Orbitron', 10)).pack(padx=10, pady=5)
        
        # Short break message
        tk.Label(msg_window, text="Short Break Message:", 
                bg='#0a0a0a', fg='#00d4ff',
                font=('Orbitron', 12, 'bold')).pack(anchor='w', padx=10, pady=(10,5))
        tk.Text(msg_window, height=3, width=60,
               bg='#1a1a1a', fg='#00d4ff',
               font=('Orbitron', 10)).pack(padx=10, pady=5)
        
        # Long break message
        tk.Label(msg_window, text="Long Break Message:", 
                bg='#0a0a0a', fg='#00d4ff',
                font=('Orbitron', 12, 'bold')).pack(anchor='w', padx=10, pady=(10,5))
        tk.Text(msg_window, height=3, width=60,
               bg='#1a1a1a', fg='#00d4ff',
               font=('Orbitron', 10)).pack(padx=10, pady=5)
        
    def setup_bindings(self):
        self.root.bind('<Escape>', lambda e: self.root.quit())
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def update_timer_display(self):
        # Update circular progress
        self.canvas.delete("all")
        
        # Draw outer ring
        self.canvas.create_oval(20, 20, 280, 280, 
                               outline='#333333', width=3, fill='#1a1a1a')
        
        # Draw progress arc
        if self.total_time > 0:
            progress = (self.total_time - self.time_left) / self.total_time
            extent = progress * 360
            
            self.canvas.create_arc(20, 20, 280, 280,
                                  start=90, extent=-extent,
                                  outline='#00d4ff', width=8,
                                  style='arc')
        
        # Draw inner circle with glow effect
        for i in range(5):
            alpha = 1 - (i * 0.15)
            color = f"#{int(0*255):02x}{int(212*alpha):02x}{int(255*alpha):02x}"
            self.canvas.create_oval(60+i*5, 60+i*5, 240-i*5, 240-i*5,
                                   outline=color, width=2)
        
        # Center dot
        self.canvas.create_oval(145, 145, 155, 155, 
                               fill='#00d4ff', outline='#00d4ff')
        
        # Update time text
        minutes = self.time_left // 60
        seconds = self.time_left % 60
        time_text = f"{minutes:02d}:{seconds:02d}"
        self.time_label.config(text=time_text)
        
    def start_timer(self):
        if not self.is_running:
            if self.time_left == 0:
                self.reset_timer()
            
            self.is_running = True
            self.is_paused = False
            self.timer_thread = threading.Thread(target=self.run_timer)
            self.timer_thread.daemon = True
            self.timer_thread.start()
            
            self.start_btn.config(text="RUNNING", state='disabled')
            
    def pause_timer(self):
        if self.is_running:
            self.is_paused = not self.is_paused
            if self.is_paused:
                self.pause_btn.config(text="RESUME")
            else:
                self.pause_btn.config(text="PAUSE")
                
    def reset_timer(self):
        self.is_running = False
        self.is_paused = False
        
        # Set time based on current session
        if self.current_session == "work":
            self.time_left = self.work_duration.get() * 60
            self.session_label.config(text="WORK SESSION")
        elif self.current_session == "short_break":
            self.time_left = self.short_break_duration.get() * 60
            self.session_label.config(text="SHORT BREAK")
        else:  # long_break
            self.time_left = self.long_break_duration.get() * 60
            self.session_label.config(text="LONG BREAK")
            
        self.total_time = self.time_left
        self.start_btn.config(text="START", state='normal')
        self.pause_btn.config(text="PAUSE")
        self.update_timer_display()
        
    def skip_session(self):
        if self.is_running:
            self.time_left = 0
            
    def run_timer(self):
        while self.is_running and self.time_left > 0:
            if not self.is_paused:
                self.time_left -= 1
                self.root.after(0, self.update_timer_display)
            time.sleep(1)
            
        if self.time_left <= 0 and self.is_running:
            self.root.after(0, self.timer_finished)
            
    def timer_finished(self):
        self.is_running = False
        self.start_btn.config(text="START", state='normal')
        
        # Show notification
        self.show_notification()
        
        # Switch to next session
        self.switch_session()
        
    def show_notification(self):
        # Voice notification
        if self.voice_enabled.get():
            message = ""
            if self.current_session == "work":
                message = self.work_message.get()
            elif self.current_session == "short_break":
                message = self.short_break_message.get()
            else:
                message = self.long_break_message.get()
                
            threading.Thread(target=self.speak_message, args=(message,)).start()
        
        # Visual notification
        if self.visual_enabled.get():
            self.show_jarvis_notification()
            
    def speak_message(self, message):
        try:
            self.tts_engine.say(message)
            self.tts_engine.runAndWait()
        except Exception as e:
            print(f"TTS Error: {e}")
            
    def show_jarvis_notification(self):
        # Create overlay window
        overlay = tk.Toplevel()
        overlay.withdraw()  # Hide initially
        
        # Configure overlay
        overlay.overrideredirect(True)
        overlay.attributes('-topmost', True)
        overlay.attributes('-alpha', 0.9)
        overlay.configure(bg='black')
        
        # Position in bottom-right corner
        screen_width = overlay.winfo_screenwidth()
        screen_height = overlay.winfo_screenheight()
        width, height = 300, 300
        x = screen_width - width - 50
        y = screen_height - height - 100
        
        overlay.geometry(f"{width}x{height}+{x}+{y}")
        
        # Create animated canvas
        canvas = tk.Canvas(overlay, width=width, height=height,
                          bg='black', highlightthickness=0)
        canvas.pack()
        
        # Show overlay
        overlay.deiconify()
        
        # Animate JARVIS-style notification
        self.animate_jarvis_effect(canvas, overlay)
        
    def animate_jarvis_effect(self, canvas, overlay):
        def animate(frame=0):
            canvas.delete("all")
            
            center_x, center_y = 150, 150
            max_frames = 60
            
            if frame >= max_frames:
                overlay.destroy()
                return
                
            # Animated rings
            for ring in range(5):
                radius = 30 + ring * 20 + (frame * 2) % 40
                alpha = 1 - (frame / max_frames)
                
                # Outer glow
                for i in range(3):
                    glow_radius = radius + i * 3
                    color_intensity = int(255 * alpha * (1 - i * 0.3))
                    color = f"#{0:02x}{int(color_intensity*0.8):02x}{color_intensity:02x}"
                    
                    canvas.create_oval(center_x - glow_radius, center_y - glow_radius,
                                     center_x + glow_radius, center_y + glow_radius,
                                     outline=color, width=2)
                
                # Main ring
                main_color = f"#{0:02x}{int(212*alpha):02x}{int(255*alpha):02x}"
                canvas.create_oval(center_x - radius, center_y - radius,
                                 center_x + radius, center_y + radius,
                                 outline=main_color, width=3)
            
            # Energy bolts
            import random
            for bolt in range(8):
                angle = (frame * 6 + bolt * 45) % 360
                start_radius = 40
                end_radius = 120
                
                start_x = center_x + start_radius * math.cos(math.radians(angle))
                start_y = center_y + start_radius * math.sin(math.radians(angle))
                end_x = center_x + end_radius * math.cos(math.radians(angle))
                end_y = center_y + end_radius * math.sin(math.radians(angle))
                
                # Add some randomness to the bolt
                end_x += random.randint(-10, 10)
                end_y += random.randint(-10, 10)
                
                alpha = 1 - (frame / max_frames)
                bolt_color = f"#{int(255*alpha):02x}{int(255*alpha):02x}{int(100*alpha):02x}"
                
                canvas.create_line(start_x, start_y, end_x, end_y,
                                 fill=bolt_color, width=2)
            
            # Central core
            core_alpha = 1 - (frame / max_frames) * 0.5
            core_color = f"#{0:02x}{int(255*core_alpha):02x}{int(255*core_alpha):02x}"
            canvas.create_oval(center_x - 15, center_y - 15,
                             center_x + 15, center_y + 15,
                             fill=core_color, outline=core_color)
            
            # Schedule next frame
            overlay.after(50, lambda: animate(frame + 1))
            
        animate()
        
    def switch_session(self):
        if self.current_session == "work":
            self.session_count += 1
            if self.session_count % 4 == 0:
                self.current_session = "long_break"
            else:
                self.current_session = "short_break"
        else:
            self.current_session = "work"
            
        self.reset_timer()
        
    def load_settings(self):
        try:
            if os.path.exists("jarvis_pomodoro_settings.json"):
                with open("jarvis_pomodoro_settings.json", "r") as f:
                    settings = json.load(f)
                    
                self.work_duration.set(settings.get("work_duration", 25))
                self.short_break_duration.set(settings.get("short_break_duration", 5))
                self.long_break_duration.set(settings.get("long_break_duration", 15))
                self.voice_enabled.set(settings.get("voice_enabled", True))
                self.visual_enabled.set(settings.get("visual_enabled", True))
                self.work_message.set(settings.get("work_message", "Time to focus. Let's get to work."))
                self.short_break_message.set(settings.get("short_break_message", "Take a short break. You've earned it."))
                self.long_break_message.set(settings.get("long_break_message", "Time for a long break. Recharge your energy."))
                
        except Exception as e:
            print(f"Error loading settings: {e}")
            
    def save_settings(self):
        try:
            settings = {
                "work_duration": self.work_duration.get(),
                "short_break_duration": self.short_break_duration.get(),
                "long_break_duration": self.long_break_duration.get(),
                "voice_enabled": self.voice_enabled.get(),
                "visual_enabled": self.visual_enabled.get(),
                "work_message": self.work_message.get(),
                "short_break_message": self.short_break_message.get(),
                "long_break_message": self.long_break_message.get()
            }
            
            with open("jarvis_pomodoro_settings.json", "w") as f:
                json.dump(settings, f, indent=4)
                
        except Exception as e:
            print(f"Error saving settings: {e}")
            
    def on_closing(self):
        self.save_settings()
        self.is_running = False
        if hasattr(self, 'tts_engine'):
            self.tts_engine.stop()
        self.root.quit()
        
    def run(self):
        # Initialize with work session
        self.reset_timer()
        
        # Start the GUI
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = JARVISPomodoroTimer()
        app.run()
    except KeyboardInterrupt:
        print("\nShutting down JARVIS Pomodoro Timer...")
        sys.exit(0)
    except Exception as e:
        print(f"Application error: {e}")
        input("Press Enter to exit...")