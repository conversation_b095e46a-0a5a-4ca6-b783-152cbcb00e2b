#!/usr/bin/env python3
"""
Test script for J.A.R.V.I.S. Pomodoro Timer
Tests core functionality without GUI
"""

import sys
import os
import time

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.timer import PomodoroTimer, TimerSettings, SessionType, TimerState
from src.settings import SettingsManager
from src.notifications.voice_notification import VoiceNotificationManager
from src.notifications.visual_notification import NotificationManager


def test_timer_functionality():
    """Test core timer functionality"""
    print("Testing Timer Functionality...")
    
    # Create timer with short durations for testing
    settings = TimerSettings(
        work_duration=3,  # 3 seconds
        short_break_duration=2,  # 2 seconds
        long_break_duration=4,  # 4 seconds
        sessions_until_long_break=2
    )
    
    timer = PomodoroTimer(settings)
    
    # Test callbacks
    def on_tick(time_remaining):
        print(f"Time remaining: {timer.format_time(time_remaining)}")
    
    def on_state_change(state):
        print(f"State changed to: {state.value}")
    
    def on_session_complete(session_type):
        print(f"Session complete: {session_type.value}")
    
    timer.on_tick = on_tick
    timer.on_state_change = on_state_change
    timer.on_session_complete = on_session_complete
    
    # Test timer operations
    print("Starting timer...")
    timer.start()
    
    # Let it run for a bit
    time.sleep(4)
    
    print("Pausing timer...")
    timer.pause()
    time.sleep(1)
    
    print("Resuming timer...")
    timer.start()
    time.sleep(3)
    
    print("Stopping timer...")
    timer.stop()
    
    print("Timer test completed!")


def test_settings_manager():
    """Test settings management"""
    print("\nTesting Settings Manager...")
    
    settings_manager = SettingsManager("test_config.json")
    
    # Test updating settings
    settings_manager.update_timer_settings(work_duration=30*60)
    settings_manager.update_notification_settings(enable_voice=False)
    settings_manager.update_ui_settings(theme_color="#FF0000")
    
    # Verify settings
    assert settings_manager.settings.timer.work_duration == 30*60
    assert settings_manager.settings.notifications.enable_voice == False
    assert settings_manager.settings.ui.theme_color == "#FF0000"
    
    print("Settings test completed!")
    
    # Clean up test file
    if os.path.exists("test_config.json"):
        os.remove("test_config.json")


def test_voice_notifications():
    """Test voice notification system"""
    print("\nTesting Voice Notifications...")
    
    try:
        voice_manager = VoiceNotificationManager()
        
        if voice_manager.engine:
            print("TTS engine initialized successfully")
            
            # Test voice settings
            voice_manager.update_settings(volume=0.5, rate=150)
            
            # Test speaking (non-blocking)
            print("Testing voice notification...")
            voice_manager.speak("J.A.R.V.I.S. Pomodoro Timer test message", blocking=False)
            
            time.sleep(2)  # Give time for speech
            
            print("Voice notification test completed!")
        else:
            print("TTS engine not available - skipping voice test")
            
    except Exception as e:
        print(f"Voice notification test failed: {e}")


def test_visual_notifications():
    """Test visual notification system"""
    print("\nTesting Visual Notifications...")
    
    try:
        # This would normally show a visual notification
        # For testing, we'll just verify the class can be instantiated
        visual_manager = NotificationManager()
        print("Visual notification manager created successfully")
        
        # Note: We can't easily test the actual visual without a display
        print("Visual notification test completed!")
        
    except Exception as e:
        print(f"Visual notification test failed: {e}")


def main():
    """Run all tests"""
    print("J.A.R.V.I.S. Pomodoro Timer - Component Tests")
    print("=" * 50)
    
    try:
        test_timer_functionality()
        test_settings_manager()
        test_voice_notifications()
        test_visual_notifications()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
