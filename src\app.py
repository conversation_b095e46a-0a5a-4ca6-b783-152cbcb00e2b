"""
Main Application Class for J.A.R.V.I.S. Pomodoro Timer
Coordinates all components and manages application lifecycle
"""

import sys
import threading
from typing import Optional

from src.timer import PomodoroTimer, SessionType
from src.settings import SettingsManager
from src.ui.main_window import <PERSON>Window
from src.ui.settings_window import SettingsWindow
from src.notifications.visual_notification import NotificationManager
from src.notifications.voice_notification import VoiceNotification<PERSON>anager, NotificationCoordinator
from src.system.tray_manager import SystemIntegration


class PomodoroApp:
    """Main application class that coordinates all components"""
    
    def __init__(self):
        # Core components
        self.settings_manager = SettingsManager()
        self.timer = PomodoroTimer(self.settings_manager.settings.timer)
        
        # UI components
        self.main_window: Optional[MainWindow] = None
        self.settings_window: Optional[SettingsWindow] = None
        
        # Notification components
        self.visual_manager = NotificationManager()
        self.voice_manager = VoiceNotificationManager()
        self.notification_coordinator = NotificationCoordinator(
            self.voice_manager, 
            self.visual_manager
        )
        
        # System integration
        self.system_integration: Optional[SystemIntegration] = None
        
        # Application state
        self.running = False
        
        self._setup_application()
    
    def _setup_application(self):
        """Setup the application components"""
        # Setup timer callbacks
        self.timer.on_session_complete = self._on_session_complete
        
        # Apply notification settings
        self._apply_notification_settings()
    
    def _apply_notification_settings(self):
        """Apply notification settings from configuration"""
        settings = self.settings_manager.settings
        
        # Voice settings
        self.voice_manager.update_settings(
            volume=settings.notifications.voice_volume,
            rate=settings.notifications.voice_rate
        )
        
        # Notification coordinator settings
        self.notification_coordinator.update_settings(
            voice_enabled=settings.notifications.enable_voice,
            visual_enabled=settings.notifications.enable_visual
        )
        
        # Update custom messages
        self.notification_coordinator.session_notifier.update_messages({
            'work_start': settings.notifications.work_start_message,
            'work_complete': settings.notifications.work_complete_message,
            'short_break_start': settings.notifications.break_start_message,
            'short_break_complete': settings.notifications.break_complete_message,
            'long_break_start': settings.notifications.long_break_start_message,
            'long_break_complete': settings.notifications.long_break_complete_message
        })
    
    def _on_session_complete(self, session_type: SessionType):
        """Handle session completion"""
        # Determine if this is a session ending or starting
        is_starting = True  # The next session is starting
        
        # Send coordinated notification
        self.notification_coordinator.notify_session_transition(
            session_type, 
            is_starting=False  # Current session is ending
        )
        
        # If auto-start is disabled, notify about the next session
        if not self._should_auto_start_next():
            next_session = self._get_next_session_type()
            threading.Timer(1.0, lambda: self.notification_coordinator.notify_session_transition(
                next_session, 
                is_starting=True
            )).start()
    
    def _should_auto_start_next(self) -> bool:
        """Check if next session should auto-start"""
        settings = self.settings_manager.settings.timer
        if self.timer.current_session == SessionType.WORK:
            return settings.auto_start_breaks
        else:
            return settings.auto_start_work
    
    def _get_next_session_type(self) -> SessionType:
        """Get the type of the next session"""
        return self.timer.current_session
    
    def _create_main_window(self):
        """Create the main window"""
        if not self.main_window:
            self.main_window = MainWindow(self.timer, self.settings_manager)
            self.main_window.on_settings_click = self._show_settings
            
            # Setup system integration
            self.system_integration = SystemIntegration(
                self.timer, 
                self.main_window, 
                self.settings_manager
            )
    
    def _show_settings(self):
        """Show the settings window"""
        if self.settings_window:
            return  # Settings window already open
        
        self.settings_window = SettingsWindow(
            self.settings_manager, 
            self.main_window.root if self.main_window else None
        )
        
        # Handle settings window close
        original_destroy = self.settings_window.window.destroy
        
        def on_settings_close():
            self._apply_notification_settings()
            if self.system_integration:
                self.system_integration.update_settings()
            self.settings_window = None
            original_destroy()
        
        self.settings_window.window.destroy = on_settings_close
    
    def run(self):
        """Start the application"""
        if self.running:
            return
        
        self.running = True
        
        try:
            # Create and show main window
            self._create_main_window()
            
            # Start the main event loop
            if self.main_window:
                self.main_window.run()
        
        except KeyboardInterrupt:
            print("Application interrupted by user")
        except Exception as e:
            print(f"Application error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.shutdown()
    
    def shutdown(self):
        """Shutdown the application"""
        if not self.running:
            return
        
        print("Shutting down J.A.R.V.I.S. Pomodoro Timer...")
        
        self.running = False
        
        # Stop timer
        if self.timer:
            self.timer.stop()
        
        # Stop notifications
        if self.voice_manager:
            self.voice_manager.stop_speaking()
        
        if self.visual_manager:
            self.visual_manager.close_all()
        
        # Stop system integration
        if self.system_integration:
            self.system_integration.shutdown()
        
        # Save settings
        if self.settings_manager:
            self.settings_manager.save_settings()
        
        # Close windows
        if self.settings_window and self.settings_window.window:
            try:
                self.settings_window.window.destroy()
            except:
                pass
        
        if self.main_window:
            try:
                self.main_window.destroy()
            except:
                pass
        
        print("Shutdown complete.")


def main():
    """Main entry point"""
    app = PomodoroApp()
    app.run()


if __name__ == "__main__":
    main()
