"""
True J.A.R.V.I.S.-Style UI with Pulsing Circular Waveform
Implements the signature J.A.R.V.I.S. aesthetic with energy ripples and sonar-like effects
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import Canvas
import math
import time
import threading
from typing import Callable, Optional, List
from src.timer import PomodoroTimer, TimerState, SessionType
from src.settings import SettingsManager


class JarvisWaveform(Canvas):
    """J.A.R.V.I.S.-style pulsing circular waveform with energy ripples"""
    
    def __init__(self, parent, size=400, **kwargs):
        super().__init__(parent, width=size, height=size, highlightthickness=0, **kwargs)
        self.size = size
        self.center = size // 2
        self.configure(bg="#000000")
        
        # Waveform properties
        self.is_active = False
        self.is_speaking = False
        self.animation_running = False
        self.animation_thread = None
        
        # Visual elements
        self.ripples = []
        self.energy_arcs = []
        self.particles = []
        self.core_pulse = 0
        self.time_offset = 0
        
        # Colors
        self.primary_color = "#00D4FF"  # Thunder blue
        self.secondary_color = "#0099CC"
        self.accent_color = "#FFD700"
        self.energy_color = "#00FFFF"
        
        # Initialize visual elements
        self._initialize_elements()
        self.start_animation()
    
    def _initialize_elements(self):
        """Initialize visual elements"""
        # Create ripples
        self.ripples = []
        for i in range(8):
            ripple = {
                'radius': 50 + (i * 25),
                'max_radius': 50 + (i * 25),
                'alpha': 1.0 - (i * 0.1),
                'speed': 0.5 + (i * 0.1),
                'phase': i * 45
            }
            self.ripples.append(ripple)
        
        # Create energy arcs
        self.energy_arcs = []
        for i in range(12):
            arc = {
                'angle': i * 30,
                'length': 60 + (i % 3) * 20,
                'speed': 1 + (i % 4) * 0.5,
                'alpha': 0.8 - (i % 4) * 0.15
            }
            self.energy_arcs.append(arc)
        
        # Create particles
        self.particles = []
        for i in range(36):
            particle = {
                'angle': i * 10,
                'radius': 100 + (i % 5) * 15,
                'size': 1 + (i % 3),
                'speed': 0.8 + (i % 3) * 0.3,
                'alpha': 0.6 + (i % 3) * 0.2,
                'orbit_speed': 0.5 + (i % 4) * 0.2,
                'type': 'normal' if i % 3 != 0 else 'energy'
            }
            self.particles.append(particle)

        # Create hexagonal elements
        self.hexagons = []
        for i in range(6):
            hex_element = {
                'angle': i * 60,
                'radius': 150 + (i % 2) * 30,
                'size': 20 + (i % 3) * 5,
                'rotation': 0,
                'alpha': 0.7 - (i % 3) * 0.1,
                'rotation_speed': 0.5 + (i % 2) * 0.3
            }
            self.hexagons.append(hex_element)
    
    def set_active(self, active: bool):
        """Set waveform active state"""
        self.is_active = active
    
    def set_speaking(self, speaking: bool):
        """Set speaking state for enhanced animation"""
        self.is_speaking = speaking
    
    def start_animation(self):
        """Start the animation loop"""
        if self.animation_running:
            return
        
        self.animation_running = True
        self.animation_thread = threading.Thread(target=self._animation_loop, daemon=True)
        self.animation_thread.start()
    
    def stop_animation(self):
        """Stop the animation loop"""
        self.animation_running = False
    
    def _animation_loop(self):
        """Main animation loop"""
        while self.animation_running:
            try:
                current_time = time.time()
                self.time_offset = current_time
                
                self._update_elements()
                self._draw_frame()
                
                time.sleep(0.033)  # ~30 FPS
                
            except Exception as e:
                print(f"Animation error: {e}")
                break
    
    def _update_elements(self):
        """Update all visual elements"""
        t = self.time_offset
        
        # Update core pulse
        base_pulse = math.sin(t * 3) * 0.3
        speaking_pulse = math.sin(t * 8) * 0.5 if self.is_speaking else 0
        active_pulse = math.sin(t * 2) * 0.2 if self.is_active else 0
        self.core_pulse = 1.0 + base_pulse + speaking_pulse + active_pulse
        
        # Update ripples
        for i, ripple in enumerate(self.ripples):
            if self.is_active or self.is_speaking:
                # Expand ripples when active
                expansion = math.sin(t * ripple['speed'] + ripple['phase'] * 0.1) * 20
                ripple['radius'] = ripple['max_radius'] + expansion
                ripple['alpha'] = (1.0 - (i * 0.08)) * (0.8 + math.sin(t * 2 + i) * 0.2)
            else:
                # Gentle idle animation
                ripple['radius'] = ripple['max_radius'] + math.sin(t * 0.5 + i) * 5
                ripple['alpha'] = (1.0 - (i * 0.12)) * 0.4
        
        # Update energy arcs
        for arc in self.energy_arcs:
            arc['angle'] = (arc['angle'] + arc['speed']) % 360
            if self.is_speaking:
                arc['alpha'] = min(1.0, arc['alpha'] * 1.5)
            elif self.is_active:
                arc['alpha'] = arc['alpha'] * 1.2
        
        # Update particles
        for particle in self.particles:
            particle['angle'] = (particle['angle'] + particle['orbit_speed']) % 360
            if self.is_active:
                particle['radius'] += math.sin(t * particle['speed']) * 2

        # Update hexagons
        for hex_element in self.hexagons:
            hex_element['rotation'] = (hex_element['rotation'] + hex_element['rotation_speed']) % 360
            if self.is_speaking:
                hex_element['alpha'] = min(1.0, hex_element['alpha'] * 1.3)
    
    def _draw_frame(self):
        """Draw a single frame"""
        try:
            self.delete("all")
            
            # Draw background grid (subtle)
            self._draw_background_grid()
            
            # Draw ripples
            self._draw_ripples()
            
            # Draw energy arcs
            self._draw_energy_arcs()
            
            # Draw particles
            self._draw_particles()

            # Draw hexagonal elements
            self._draw_hexagons()

            # Draw central core
            self._draw_core()

            # Draw HUD elements
            self._draw_hud_elements()
            
            self.update()
            
        except tk.TclError:
            self.animation_running = False
    
    def _draw_background_grid(self):
        """Draw subtle background grid"""
        grid_color = "#001122"
        spacing = 40
        
        # Vertical lines
        for x in range(0, self.size, spacing):
            self.create_line(x, 0, x, self.size, fill=grid_color, width=1)
        
        # Horizontal lines
        for y in range(0, self.size, spacing):
            self.create_line(0, y, self.size, y, fill=grid_color, width=1)
    
    def _draw_ripples(self):
        """Draw energy ripples"""
        for ripple in self.ripples:
            if ripple['alpha'] <= 0:
                continue
            
            color = self._get_alpha_color(self.primary_color, ripple['alpha'])
            
            # Main ripple
            self.create_oval(
                self.center - ripple['radius'], self.center - ripple['radius'],
                self.center + ripple['radius'], self.center + ripple['radius'],
                outline=color, width=2, fill=""
            )
            
            # Inner glow
            if ripple['alpha'] > 0.5:
                glow_color = self._get_alpha_color(self.energy_color, ripple['alpha'] * 0.3)
                self.create_oval(
                    self.center - ripple['radius'] + 2, self.center - ripple['radius'] + 2,
                    self.center + ripple['radius'] - 2, self.center + ripple['radius'] - 2,
                    outline=glow_color, width=1, fill=""
                )
    
    def _draw_energy_arcs(self):
        """Draw rotating energy arcs"""
        for arc in self.energy_arcs:
            if arc['alpha'] <= 0:
                continue
            
            angle_rad = math.radians(arc['angle'])
            start_radius = 80
            end_radius = start_radius + arc['length']
            
            # Calculate arc endpoints
            start_x = self.center + start_radius * math.cos(angle_rad)
            start_y = self.center + start_radius * math.sin(angle_rad)
            end_x = self.center + end_radius * math.cos(angle_rad)
            end_y = self.center + end_radius * math.sin(angle_rad)
            
            color = self._get_alpha_color(self.secondary_color, arc['alpha'])
            
            # Draw arc line
            self.create_line(start_x, start_y, end_x, end_y, fill=color, width=2)
            
            # Draw arc endpoint
            self.create_oval(end_x-2, end_y-2, end_x+2, end_y+2, fill=color, outline="")
    
    def _draw_particles(self):
        """Draw orbiting particles"""
        for particle in self.particles:
            angle_rad = math.radians(particle['angle'])
            x = self.center + particle['radius'] * math.cos(angle_rad)
            y = self.center + particle['radius'] * math.sin(angle_rad)

            if particle['type'] == 'energy':
                # Energy particles with trails
                color = self._get_alpha_color(self.energy_color, particle['alpha'])
                size = particle['size'] * 1.5

                # Draw trail
                trail_length = 10
                for i in range(trail_length):
                    trail_angle = particle['angle'] - (i * 2)
                    trail_rad = math.radians(trail_angle)
                    trail_x = self.center + particle['radius'] * math.cos(trail_rad)
                    trail_y = self.center + particle['radius'] * math.sin(trail_rad)
                    trail_alpha = particle['alpha'] * (1 - i / trail_length)
                    trail_color = self._get_alpha_color(self.energy_color, trail_alpha)
                    trail_size = size * (1 - i / trail_length)

                    self.create_oval(
                        trail_x - trail_size, trail_y - trail_size,
                        trail_x + trail_size, trail_y + trail_size,
                        fill=trail_color, outline=""
                    )
            else:
                # Normal particles
                color = self._get_alpha_color(self.accent_color, particle['alpha'])
                size = particle['size']

                self.create_oval(x-size, y-size, x+size, y+size, fill=color, outline="")

    def _draw_hexagons(self):
        """Draw hexagonal elements"""
        for hex_element in self.hexagons:
            if hex_element['alpha'] <= 0:
                continue

            angle_rad = math.radians(hex_element['angle'])
            x = self.center + hex_element['radius'] * math.cos(angle_rad)
            y = self.center + hex_element['radius'] * math.sin(angle_rad)

            color = self._get_alpha_color(self.secondary_color, hex_element['alpha'])

            # Draw hexagon
            self._draw_hexagon(x, y, hex_element['size'], hex_element['rotation'], color)

    def _draw_hexagon(self, center_x, center_y, size, rotation, color):
        """Draw a hexagon at specified position"""
        points = []
        for i in range(6):
            angle = math.radians(rotation + i * 60)
            x = center_x + size * math.cos(angle)
            y = center_y + size * math.sin(angle)
            points.extend([x, y])

        self.create_polygon(points, outline=color, fill="", width=2)
    
    def _draw_core(self):
        """Draw central energy core"""
        core_radius = 30 * self.core_pulse
        
        # Multiple layers for glow effect
        for i in range(6):
            radius = core_radius + (i * 4)
            alpha = 1.0 - (i * 0.15)
            color = self._get_alpha_color(self.primary_color, alpha)
            
            self.create_oval(
                self.center - radius, self.center - radius,
                self.center + radius, self.center + radius,
                fill=color, outline=""
            )
        
        # Central bright core
        inner_radius = core_radius * 0.3
        self.create_oval(
            self.center - inner_radius, self.center - inner_radius,
            self.center + inner_radius, self.center + inner_radius,
            fill="#FFFFFF", outline=""
        )
    
    def _draw_hud_elements(self):
        """Draw HUD-style elements"""
        # Corner brackets
        bracket_size = 20
        bracket_color = self.secondary_color
        
        # Top-left
        self.create_line(10, 10, 10 + bracket_size, 10, fill=bracket_color, width=2)
        self.create_line(10, 10, 10, 10 + bracket_size, fill=bracket_color, width=2)
        
        # Top-right
        self.create_line(self.size - 10, 10, self.size - 10 - bracket_size, 10, fill=bracket_color, width=2)
        self.create_line(self.size - 10, 10, self.size - 10, 10 + bracket_size, fill=bracket_color, width=2)
        
        # Bottom-left
        self.create_line(10, self.size - 10, 10 + bracket_size, self.size - 10, fill=bracket_color, width=2)
        self.create_line(10, self.size - 10, 10, self.size - 10 - bracket_size, fill=bracket_color, width=2)
        
        # Bottom-right
        self.create_line(self.size - 10, self.size - 10, self.size - 10 - bracket_size, self.size - 10, fill=bracket_color, width=2)
        self.create_line(self.size - 10, self.size - 10, self.size - 10, self.size - 10 - bracket_size, fill=bracket_color, width=2)
    
    def _get_alpha_color(self, color: str, alpha: float) -> str:
        """Apply alpha to color"""
        if color.startswith("#"):
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
            
            r = int(r * alpha)
            g = int(g * alpha)
            b = int(b * alpha)
            
            return f"#{r:02x}{g:02x}{b:02x}"
        return color


class JarvisButton(ctk.CTkButton):
    """J.A.R.V.I.S.-style hexagonal button"""

    def __init__(self, parent, **kwargs):
        # J.A.R.V.I.S. button styling
        default_kwargs = {
            "corner_radius": 0,
            "border_width": 2,
            "border_color": "#00D4FF",
            "fg_color": "transparent",
            "hover_color": "#00D4FF",
            "text_color": "#00D4FF",
            "font": ("Consolas", 12, "bold"),
            "height": 40,
            "width": 120
        }
        default_kwargs.update(kwargs)
        super().__init__(parent, **default_kwargs)


class JarvisLabel(ctk.CTkLabel):
    """J.A.R.V.I.S.-style label with glow effect"""

    def __init__(self, parent, **kwargs):
        default_kwargs = {
            "text_color": "#00D4FF",
            "font": ("Consolas", 14, "bold")
        }
        default_kwargs.update(kwargs)
        super().__init__(parent, **default_kwargs)


class JarvisMainWindow:
    """Main J.A.R.V.I.S.-style interface"""

    def __init__(self, timer: PomodoroTimer, settings_manager: SettingsManager):
        self.timer = timer
        self.settings = settings_manager
        self.root = None
        self.waveform = None
        self.time_label = None
        self.session_label = None
        self.status_label = None

        # Control buttons
        self.start_button = None
        self.pause_button = None
        self.stop_button = None
        self.skip_button = None
        self.settings_button = None

        # Callbacks
        self.on_settings_click: Optional[Callable] = None

        self._setup_ui()
        self._setup_timer_callbacks()

    def _setup_ui(self):
        """Initialize the J.A.R.V.I.S. UI"""
        # Configure CustomTkinter
        ctk.set_appearance_mode("dark")

        # Create main window
        self.root = ctk.CTk()
        self.root.title("J.A.R.V.I.S. POMODORO SYSTEM")
        self.root.geometry("600x700")
        self.root.configure(fg_color="#000000")

        if self.settings.settings.ui.always_on_top:
            self.root.attributes("-topmost", True)

        # Main container
        main_frame = ctk.CTkFrame(self.root, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Header
        self._create_header(main_frame)

        # Central waveform
        self._create_waveform(main_frame)

        # Timer display
        self._create_timer_display(main_frame)

        # Control panel
        self._create_control_panel(main_frame)

        # Status bar
        self._create_status_bar(main_frame)

    def _create_header(self, parent):
        """Create the header section"""
        header_frame = ctk.CTkFrame(parent, fg_color="transparent")
        header_frame.pack(fill="x", pady=(0, 20))

        # Main title
        title_label = JarvisLabel(
            header_frame,
            text="J.A.R.V.I.S.",
            font=("Consolas", 28, "bold"),
            text_color="#00D4FF"
        )
        title_label.pack()

        # Subtitle
        subtitle_label = JarvisLabel(
            header_frame,
            text="POMODORO PRODUCTIVITY SYSTEM",
            font=("Consolas", 12),
            text_color="#0099CC"
        )
        subtitle_label.pack()

        # Divider line
        divider = ctk.CTkFrame(header_frame, height=2, fg_color="#00D4FF")
        divider.pack(fill="x", pady=10)

    def _create_waveform(self, parent):
        """Create the central waveform display"""
        waveform_frame = ctk.CTkFrame(parent, fg_color="transparent")
        waveform_frame.pack(pady=20)

        self.waveform = JarvisWaveform(waveform_frame, size=400)
        self.waveform.pack()

    def _create_timer_display(self, parent):
        """Create timer information display"""
        timer_frame = ctk.CTkFrame(parent, fg_color="#001122", border_width=2, border_color="#00D4FF")
        timer_frame.pack(fill="x", pady=20)

        # Time display
        self.time_label = JarvisLabel(
            timer_frame,
            text="25:00",
            font=("Consolas", 48, "bold"),
            text_color="#00FFFF"
        )
        self.time_label.pack(pady=10)

        # Session info
        self.session_label = JarvisLabel(
            timer_frame,
            text="WORK SESSION #1",
            font=("Consolas", 16, "bold"),
            text_color="#FFD700"
        )
        self.session_label.pack(pady=(0, 10))

    def _create_control_panel(self, parent):
        """Create control buttons"""
        control_frame = ctk.CTkFrame(parent, fg_color="transparent")
        control_frame.pack(pady=20)

        # Primary controls
        primary_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        primary_frame.pack()

        self.start_button = JarvisButton(
            primary_frame,
            text="► START",
            command=self._on_start_click,
            fg_color="#00D4FF",
            hover_color="#0099CC",
            text_color="#000000",
            width=140
        )
        self.start_button.grid(row=0, column=0, padx=10)

        self.pause_button = JarvisButton(
            primary_frame,
            text="⏸ PAUSE",
            command=self._on_pause_click,
            width=140
        )
        self.pause_button.grid(row=0, column=1, padx=10)

        self.stop_button = JarvisButton(
            primary_frame,
            text="⏹ STOP",
            command=self._on_stop_click,
            width=140
        )
        self.stop_button.grid(row=0, column=2, padx=10)

        # Secondary controls
        secondary_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        secondary_frame.pack(pady=(15, 0))

        self.skip_button = JarvisButton(
            secondary_frame,
            text="⏭ SKIP SESSION",
            command=self._on_skip_click,
            width=200
        )
        self.skip_button.grid(row=0, column=0, padx=10)

        self.settings_button = JarvisButton(
            secondary_frame,
            text="⚙ SETTINGS",
            command=self._on_settings_click,
            width=200
        )
        self.settings_button.grid(row=0, column=1, padx=10)

    def _create_status_bar(self, parent):
        """Create status information bar"""
        status_frame = ctk.CTkFrame(parent, fg_color="#001122", border_width=1, border_color="#0099CC")
        status_frame.pack(fill="x", pady=(20, 0))

        self.status_label = JarvisLabel(
            status_frame,
            text="SYSTEM READY",
            font=("Consolas", 10),
            text_color="#00D4FF"
        )
        self.status_label.pack(pady=5)

    def _setup_timer_callbacks(self):
        """Setup timer event callbacks"""
        self.timer.on_tick = self._on_timer_tick
        self.timer.on_state_change = self._on_timer_state_change
        self.timer.on_session_complete = self._on_session_complete

    def _on_timer_tick(self, time_remaining: int):
        """Handle timer tick updates"""
        if self.time_label:
            self.time_label.configure(text=self.timer.format_time(time_remaining))

        # Update waveform based on timer activity
        if self.waveform:
            self.waveform.set_active(self.timer.state == TimerState.RUNNING)

    def _on_timer_state_change(self, state: TimerState):
        """Handle timer state changes"""
        self._update_button_states()
        self._update_session_display()
        self._update_status(f"TIMER {state.value.upper()}")

    def _on_session_complete(self, session_type: SessionType):
        """Handle session completion"""
        self._update_session_display()
        self._update_status(f"{session_type.value.upper()} SESSION COMPLETE")

    def _update_button_states(self):
        """Update button states based on timer state"""
        if not self.start_button:
            return

        state = self.timer.state

        if state == TimerState.STOPPED:
            self.start_button.configure(state="normal", text="► START")
            self.pause_button.configure(state="disabled")
            self.stop_button.configure(state="disabled")
        elif state == TimerState.RUNNING:
            self.start_button.configure(state="disabled")
            self.pause_button.configure(state="normal")
            self.stop_button.configure(state="normal")
        elif state == TimerState.PAUSED:
            self.start_button.configure(state="normal", text="► RESUME")
            self.pause_button.configure(state="disabled")
            self.stop_button.configure(state="normal")

    def _update_session_display(self):
        """Update session type display"""
        if not self.session_label:
            return

        session_type = self.timer.current_session
        session_count = self.timer.session_count

        if session_type == SessionType.WORK:
            text = f"WORK SESSION #{session_count + 1}"
            color = "#00FFFF"
        elif session_type == SessionType.SHORT_BREAK:
            text = "SHORT BREAK"
            color = "#00FF88"
        else:  # LONG_BREAK
            text = "LONG BREAK"
            color = "#FF8800"

        self.session_label.configure(text=text, text_color=color)

    def _update_status(self, message: str):
        """Update status message"""
        if self.status_label:
            self.status_label.configure(text=message)

    def set_speaking(self, speaking: bool):
        """Set speaking state for waveform animation"""
        if self.waveform:
            self.waveform.set_speaking(speaking)

    def _on_start_click(self):
        """Handle start button click"""
        self.timer.start()

    def _on_pause_click(self):
        """Handle pause button click"""
        self.timer.pause()

    def _on_stop_click(self):
        """Handle stop button click"""
        self.timer.stop()

    def _on_skip_click(self):
        """Handle skip button click"""
        self.timer.skip_session()

    def _on_settings_click(self):
        """Handle settings button click"""
        if self.on_settings_click:
            self.on_settings_click()

    def run(self):
        """Start the main event loop"""
        if self.root:
            self.root.mainloop()

    def destroy(self):
        """Clean up and destroy window"""
        if self.waveform:
            self.waveform.stop_animation()
        if self.root:
            self.root.destroy()
