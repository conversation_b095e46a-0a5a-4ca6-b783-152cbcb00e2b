"""
System Tray Manager for J.A.R.V.I.S. Pomodoro Timer
Handles system tray integration and persistent background operation
"""

import pystray
from PIL import Image, ImageDraw
import threading
from typing import Callable, Optional
from src.timer import PomodoroTimer, TimerState, SessionType


class TrayManager:
    """Manages system tray icon and menu"""
    
    def __init__(self, timer: PomodoroTimer, show_window_callback: Callable = None):
        self.timer = timer
        self.show_window_callback = show_window_callback
        self.icon: Optional[pystray.Icon] = None
        self.running = False
        
        # Create tray icon
        self._create_icon()
        self._setup_timer_callbacks()
    
    def _create_icon(self):
        """Create the system tray icon"""
        # Create icon image
        image = self._create_icon_image()
        
        # Create menu
        menu = pystray.Menu(
            pystray.MenuItem("Show J.A.R.V.I.S.", self._show_window, default=True),
            pystray.MenuItem("Start Timer", self._start_timer, enabled=lambda item: self.timer.state == TimerState.STOPPED),
            pystray.MenuItem("Pause Timer", self._pause_timer, enabled=lambda item: self.timer.state == TimerState.RUNNING),
            pystray.MenuItem("Stop Timer", self._stop_timer, enabled=lambda item: self.timer.state != TimerState.STOPPED),
            pystray.MenuItem("Skip Session", self._skip_session, enabled=lambda item: self.timer.state != TimerState.STOPPED),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem("Exit", self._quit_application)
        )
        
        # Create icon
        self.icon = pystray.Icon(
            "jarvis_pomodoro",
            image,
            "J.A.R.V.I.S. Pomodoro Timer",
            menu
        )
    
    def _create_icon_image(self, size: int = 64) -> Image.Image:
        """Create the tray icon image"""
        # Create image
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # Get current session color
        color = self._get_current_color()
        
        # Draw circular icon with J.A.R.V.I.S. style
        center = size // 2
        
        # Outer ring
        ring_width = 4
        draw.ellipse(
            [ring_width, ring_width, size - ring_width, size - ring_width],
            outline=color,
            width=ring_width
        )
        
        # Inner circle based on timer state
        if self.timer.state == TimerState.RUNNING:
            # Filled circle for running
            inner_size = size // 3
            draw.ellipse(
                [center - inner_size//2, center - inner_size//2, 
                 center + inner_size//2, center + inner_size//2],
                fill=color
            )
        elif self.timer.state == TimerState.PAUSED:
            # Pause symbol
            bar_width = 3
            bar_height = size // 3
            bar_spacing = 4
            
            # Left bar
            draw.rectangle(
                [center - bar_spacing - bar_width, center - bar_height//2,
                 center - bar_spacing, center + bar_height//2],
                fill=color
            )
            
            # Right bar
            draw.rectangle(
                [center + bar_spacing, center - bar_height//2,
                 center + bar_spacing + bar_width, center + bar_height//2],
                fill=color
            )
        else:
            # Play symbol for stopped
            triangle_size = size // 4
            points = [
                (center - triangle_size//2, center - triangle_size//2),
                (center - triangle_size//2, center + triangle_size//2),
                (center + triangle_size//2, center)
            ]
            draw.polygon(points, fill=color)
        
        return image
    
    def _get_current_color(self) -> tuple:
        """Get color based on current session"""
        if self.timer.current_session == SessionType.WORK:
            return (0, 212, 255)  # Blue
        elif self.timer.current_session == SessionType.SHORT_BREAK:
            return (0, 255, 136)  # Green
        else:  # LONG_BREAK
            return (255, 136, 0)  # Orange
    
    def _setup_timer_callbacks(self):
        """Setup timer callbacks for tray updates"""
        original_on_state_change = self.timer.on_state_change
        original_on_session_complete = self.timer.on_session_complete
        
        def on_state_change(state):
            if original_on_state_change:
                original_on_state_change(state)
            self._update_icon()
        
        def on_session_complete(session_type):
            if original_on_session_complete:
                original_on_session_complete(session_type)
            self._update_icon()
            self._show_tray_notification(session_type)
        
        self.timer.on_state_change = on_state_change
        self.timer.on_session_complete = on_session_complete
    
    def _update_icon(self):
        """Update the tray icon"""
        if self.icon:
            new_image = self._create_icon_image()
            self.icon.icon = new_image
    
    def _show_tray_notification(self, session_type: SessionType):
        """Show system tray notification"""
        if not self.icon:
            return
        
        # Determine notification message
        if session_type == SessionType.WORK:
            title = "Work Session Complete!"
            message = "Time for a break. Great job!"
        elif session_type == SessionType.SHORT_BREAK:
            title = "Break Complete!"
            message = "Ready to get back to work?"
        else:  # LONG_BREAK
            title = "Long Break Complete!"
            message = "Refreshed and ready to continue!"
        
        try:
            self.icon.notify(message, title)
        except Exception as e:
            print(f"Error showing tray notification: {e}")
    
    def _show_window(self, icon=None, item=None):
        """Show the main window"""
        if self.show_window_callback:
            self.show_window_callback()
    
    def _start_timer(self, icon=None, item=None):
        """Start the timer from tray"""
        self.timer.start()
    
    def _pause_timer(self, icon=None, item=None):
        """Pause the timer from tray"""
        self.timer.pause()
    
    def _stop_timer(self, icon=None, item=None):
        """Stop the timer from tray"""
        self.timer.stop()
    
    def _skip_session(self, icon=None, item=None):
        """Skip current session from tray"""
        self.timer.skip_session()
    
    def _quit_application(self, icon=None, item=None):
        """Quit the application"""
        self.stop()
        # Note: The main application should handle the actual exit
    
    def start(self):
        """Start the tray icon"""
        if self.icon and not self.running:
            self.running = True
            # Run in separate thread to avoid blocking
            tray_thread = threading.Thread(target=self.icon.run, daemon=True)
            tray_thread.start()
    
    def stop(self):
        """Stop the tray icon"""
        if self.icon and self.running:
            self.running = False
            self.icon.stop()


class WindowManager:
    """Manages window state and always-on-top functionality"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.is_hidden = False
        self.always_on_top = True
    
    def set_always_on_top(self, enabled: bool):
        """Set always-on-top behavior"""
        self.always_on_top = enabled
        if self.main_window and self.main_window.root:
            self.main_window.root.attributes("-topmost", enabled)
    
    def hide_window(self):
        """Hide the main window"""
        if self.main_window and self.main_window.root and not self.is_hidden:
            self.main_window.root.withdraw()
            self.is_hidden = True
    
    def show_window(self):
        """Show the main window"""
        if self.main_window and self.main_window.root and self.is_hidden:
            self.main_window.root.deiconify()
            self.main_window.root.lift()
            self.main_window.root.focus_force()
            self.is_hidden = False
    
    def toggle_window(self):
        """Toggle window visibility"""
        if self.is_hidden:
            self.show_window()
        else:
            self.hide_window()
    
    def setup_window_events(self):
        """Setup window event handlers"""
        if self.main_window and self.main_window.root:
            # Handle window close event
            self.main_window.root.protocol("WM_DELETE_WINDOW", self.hide_window)
            
            # Handle minimize event (optional)
            self.main_window.root.bind("<Unmap>", self._on_window_minimize)
    
    def _on_window_minimize(self, event):
        """Handle window minimize event"""
        # Only hide if the event is for the main window
        if event.widget == self.main_window.root:
            self.hide_window()


class SystemIntegration:
    """Handles system-level integration features"""
    
    def __init__(self, timer: PomodoroTimer, main_window, settings_manager):
        self.timer = timer
        self.main_window = main_window
        self.settings_manager = settings_manager
        
        # Managers
        self.tray_manager = TrayManager(timer, self._show_window)
        self.window_manager = WindowManager(main_window)
        
        self._setup_integration()
    
    def _setup_integration(self):
        """Setup system integration"""
        # Setup window management
        self.window_manager.setup_window_events()
        
        # Apply settings
        settings = self.settings_manager.settings
        self.window_manager.set_always_on_top(settings.ui.always_on_top)
        
        # Start tray if enabled
        if settings.ui.minimize_to_tray:
            self.tray_manager.start()
    
    def _show_window(self):
        """Show window callback for tray"""
        self.window_manager.show_window()
    
    def update_settings(self):
        """Update integration based on settings"""
        settings = self.settings_manager.settings
        self.window_manager.set_always_on_top(settings.ui.always_on_top)
    
    def shutdown(self):
        """Shutdown system integration"""
        self.tray_manager.stop()
