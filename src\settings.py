"""
Settings management for the Pomodoro Timer
Handles configuration persistence and customization
"""

import json
import os
from dataclasses import dataclass, asdict
from typing import Dict, Any
from src.timer import TimerSettings


@dataclass
class NotificationSettings:
    """Notification configuration"""
    enable_voice: bool = True
    enable_visual: bool = True
    voice_volume: float = 0.8
    voice_rate: int = 200
    
    # Custom messages
    work_start_message: str = "Time to focus. Let's get to work."
    work_complete_message: str = "Great work! Time for a break."
    break_start_message: str = "Break time. Relax and recharge."
    break_complete_message: str = "Break's over. Ready to focus again?"
    long_break_start_message: str = "You've earned a long break. Take your time."
    long_break_complete_message: str = "Refreshed and ready. Let's continue the great work."


@dataclass
class UISettings:
    """UI configuration"""
    theme_color: str = "#00D4FF"  # Thunder blue
    secondary_color: str = "#0099CC"
    background_color: str = "#1A1A1A"
    text_color: str = "#FFFFFF"
    accent_color: str = "#FFD700"
    
    window_width: int = 400
    window_height: int = 500
    always_on_top: bool = True
    minimize_to_tray: bool = True
    
    # Animation settings
    enable_animations: bool = True
    animation_speed: float = 1.0
    glow_intensity: float = 0.8


@dataclass
class AppSettings:
    """Complete application settings"""
    timer: TimerSettings
    notifications: NotificationSettings
    ui: UISettings
    
    def __init__(self):
        self.timer = TimerSettings()
        self.notifications = NotificationSettings()
        self.ui = UISettings()


class SettingsManager:
    """Manages application settings with persistence"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.settings = AppSettings()
        self.load_settings()
    
    def load_settings(self):
        """Load settings from file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                
                # Load timer settings
                if 'timer' in data:
                    timer_data = data['timer']
                    self.settings.timer = TimerSettings(
                        work_duration=timer_data.get('work_duration', 25 * 60),
                        short_break_duration=timer_data.get('short_break_duration', 5 * 60),
                        long_break_duration=timer_data.get('long_break_duration', 15 * 60),
                        sessions_until_long_break=timer_data.get('sessions_until_long_break', 4),
                        auto_start_breaks=timer_data.get('auto_start_breaks', False),
                        auto_start_work=timer_data.get('auto_start_work', False)
                    )
                
                # Load notification settings
                if 'notifications' in data:
                    notif_data = data['notifications']
                    self.settings.notifications = NotificationSettings(
                        enable_voice=notif_data.get('enable_voice', True),
                        enable_visual=notif_data.get('enable_visual', True),
                        voice_volume=notif_data.get('voice_volume', 0.8),
                        voice_rate=notif_data.get('voice_rate', 200),
                        work_start_message=notif_data.get('work_start_message', 
                                                       "Time to focus. Let's get to work."),
                        work_complete_message=notif_data.get('work_complete_message', 
                                                           "Great work! Time for a break."),
                        break_start_message=notif_data.get('break_start_message', 
                                                         "Break time. Relax and recharge."),
                        break_complete_message=notif_data.get('break_complete_message', 
                                                            "Break's over. Ready to focus again?"),
                        long_break_start_message=notif_data.get('long_break_start_message', 
                                                              "You've earned a long break. Take your time."),
                        long_break_complete_message=notif_data.get('long_break_complete_message', 
                                                                 "Refreshed and ready. Let's continue the great work.")
                    )
                
                # Load UI settings
                if 'ui' in data:
                    ui_data = data['ui']
                    self.settings.ui = UISettings(
                        theme_color=ui_data.get('theme_color', "#00D4FF"),
                        secondary_color=ui_data.get('secondary_color', "#0099CC"),
                        background_color=ui_data.get('background_color', "#1A1A1A"),
                        text_color=ui_data.get('text_color', "#FFFFFF"),
                        accent_color=ui_data.get('accent_color', "#FFD700"),
                        window_width=ui_data.get('window_width', 400),
                        window_height=ui_data.get('window_height', 500),
                        always_on_top=ui_data.get('always_on_top', True),
                        minimize_to_tray=ui_data.get('minimize_to_tray', True),
                        enable_animations=ui_data.get('enable_animations', True),
                        animation_speed=ui_data.get('animation_speed', 1.0),
                        glow_intensity=ui_data.get('glow_intensity', 0.8)
                    )
                
            except (json.JSONDecodeError, KeyError, TypeError) as e:
                print(f"Error loading settings: {e}. Using defaults.")
                self.settings = AppSettings()
    
    def save_settings(self):
        """Save settings to file"""
        try:
            data = {
                'timer': asdict(self.settings.timer),
                'notifications': asdict(self.settings.notifications),
                'ui': asdict(self.settings.ui)
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            print(f"Error saving settings: {e}")
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        self.settings = AppSettings()
        self.save_settings()
    
    def update_timer_settings(self, **kwargs):
        """Update timer settings"""
        for key, value in kwargs.items():
            if hasattr(self.settings.timer, key):
                setattr(self.settings.timer, key, value)
        self.save_settings()
    
    def update_notification_settings(self, **kwargs):
        """Update notification settings"""
        for key, value in kwargs.items():
            if hasattr(self.settings.notifications, key):
                setattr(self.settings.notifications, key, value)
        self.save_settings()
    
    def update_ui_settings(self, **kwargs):
        """Update UI settings"""
        for key, value in kwargs.items():
            if hasattr(self.settings.ui, key):
                setattr(self.settings.ui, key, value)
        self.save_settings()
    
    def get_voice_message(self, session_type: str, is_starting: bool) -> str:
        """Get appropriate voice message for session transition"""
        notifications = self.settings.notifications
        
        if session_type == "work":
            return notifications.work_start_message if is_starting else notifications.work_complete_message
        elif session_type == "short_break":
            return notifications.break_start_message if is_starting else notifications.break_complete_message
        elif session_type == "long_break":
            return notifications.long_break_start_message if is_starting else notifications.long_break_complete_message
        
        return "Session transition"
