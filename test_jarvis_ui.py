#!/usr/bin/env python3
"""
Test script for the new J.A.R.V.I.S. UI
Tests the pulsing waveform and settings integration
"""

import sys
import os
import time

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.timer import PomodoroTimer, TimerSettings
from src.settings import SettingsManager
from src.ui.jarvis_ui import JarvisWaveform, JarvisMainWindow


def test_waveform():
    """Test the J.A.R.V.I.S. waveform component"""
    print("🤖 Testing J.A.R.V.I.S. Waveform...")
    
    try:
        import tkinter as tk
        
        # Create a simple test window
        root = tk.Tk()
        root.title("J.A.R.V.I.S. Waveform Test")
        root.geometry("500x500")
        root.configure(bg="#000000")
        
        # Create waveform
        waveform = JarvisWaveform(root, size=400)
        waveform.pack(expand=True)
        
        print("  ✓ Waveform created successfully")
        
        # Test different states
        def test_states():
            print("  Testing active state...")
            waveform.set_active(True)
            root.after(2000, lambda: waveform.set_speaking(True))
            root.after(4000, lambda: waveform.set_speaking(False))
            root.after(6000, lambda: waveform.set_active(False))
            root.after(8000, root.quit)
        
        root.after(1000, test_states)
        
        print("  Running waveform test for 8 seconds...")
        root.mainloop()
        
        print("  ✓ Waveform test completed successfully")
        
    except Exception as e:
        print(f"  ❌ Waveform test failed: {e}")


def test_settings_integration():
    """Test settings integration"""
    print("⚙ Testing Settings Integration...")
    
    try:
        # Create settings manager
        settings_manager = SettingsManager("test_jarvis_config.json")
        
        # Test updating settings
        original_work_duration = settings_manager.settings.timer.work_duration
        settings_manager.update_timer_settings(work_duration=30*60)
        
        # Verify change
        assert settings_manager.settings.timer.work_duration == 30*60
        print("  ✓ Timer settings update works")
        
        # Test UI settings
        settings_manager.update_ui_settings(theme_color="#FF0000")
        assert settings_manager.settings.ui.theme_color == "#FF0000"
        print("  ✓ UI settings update works")
        
        # Test notification settings
        settings_manager.update_notification_settings(enable_voice=False)
        assert settings_manager.settings.notifications.enable_voice == False
        print("  ✓ Notification settings update works")
        
        # Clean up
        if os.path.exists("test_jarvis_config.json"):
            os.remove("test_jarvis_config.json")
        
        print("  ✓ Settings integration test completed")
        
    except Exception as e:
        print(f"  ❌ Settings integration test failed: {e}")


def test_timer_integration():
    """Test timer integration with new UI"""
    print("⏱ Testing Timer Integration...")
    
    try:
        # Create components
        settings = TimerSettings(work_duration=3, short_break_duration=2)
        timer = PomodoroTimer(settings)
        settings_manager = SettingsManager()
        
        # Test timer callbacks
        callback_called = False
        
        def on_tick(time_remaining):
            nonlocal callback_called
            callback_called = True
            print(f"    ⏰ Tick: {timer.format_time(time_remaining)}")
        
        timer.on_tick = on_tick
        
        # Start timer briefly
        timer.start()
        time.sleep(1)
        timer.stop()
        
        assert callback_called, "Timer callback not called"
        print("  ✓ Timer callbacks work correctly")
        
        print("  ✓ Timer integration test completed")
        
    except Exception as e:
        print(f"  ❌ Timer integration test failed: {e}")


def main():
    """Run all tests"""
    print("🤖 J.A.R.V.I.S. UI Integration Tests")
    print("=" * 50)
    print()
    
    try:
        test_settings_integration()
        print()
        
        test_timer_integration()
        print()
        
        # Only test waveform if display is available
        try:
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # Hide window
            root.destroy()
            
            test_waveform()
            print()
            
        except Exception:
            print("🎨 Waveform test skipped (no display available)")
            print()
        
        print("=" * 50)
        print("🎉 All J.A.R.V.I.S. UI tests completed!")
        print()
        print("✅ Key Features Verified:")
        print("  • Settings integration and persistence")
        print("  • Timer functionality and callbacks")
        print("  • J.A.R.V.I.S. waveform animation")
        print("  • Component integration")
        print()
        print("🚀 Ready to launch full application:")
        print("    python main.py")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
