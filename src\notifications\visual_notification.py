"""
J.A.R.V.I.S.-Style Visual Notification System
Creates animated circular overlays with energy effects
"""

import tkinter as tk
from tkinter import Canvas
import math
import threading
import time
from typing import Optional, Callable


class JarvisNotification:
    """J.A.R.V.I.S.-inspired visual notification overlay"""
    
    def __init__(self, message: str, duration: float = 3.0, color: str = "#00D4FF"):
        self.message = message
        self.duration = duration
        self.color = color
        self.window: Optional[tk.Toplevel] = None
        self.canvas: Optional[Canvas] = None
        
        # Animation properties
        self.animation_running = False
        self.animation_thread: Optional[threading.Thread] = None
        self.start_time = 0
        self.size = 300
        self.center = self.size // 2
        
        # Energy ring properties
        self.rings = []
        self.particles = []
        self.pulse_phase = 0
        
        self._create_notification()
    
    def _create_notification(self):
        """Create the notification window"""
        self.window = tk.Toplevel()
        self.window.title("")
        self.window.configure(bg='black')
        
        # Remove window decorations
        self.window.overrideredirect(True)
        self.window.attributes('-topmost', True)
        self.window.attributes('-alpha', 0.9)
        
        # Position in bottom-right corner
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = screen_width - self.size - 50
        y = screen_height - self.size - 100
        
        self.window.geometry(f"{self.size}x{self.size}+{x}+{y}")
        
        # Create canvas
        self.canvas = Canvas(
            self.window,
            width=self.size,
            height=self.size,
            bg='black',
            highlightthickness=0
        )
        self.canvas.pack()
        
        # Initialize animation elements
        self._initialize_rings()
        self._initialize_particles()
        
        # Start animation
        self.start_animation()
    
    def _initialize_rings(self):
        """Initialize energy rings"""
        self.rings = []
        for i in range(5):
            ring = {
                'radius': 30 + (i * 25),
                'width': 3 - (i * 0.4),
                'alpha': 1.0 - (i * 0.15),
                'rotation': i * 45,
                'speed': 2 + (i * 0.5)
            }
            self.rings.append(ring)
    
    def _initialize_particles(self):
        """Initialize energy particles"""
        self.particles = []
        for i in range(20):
            particle = {
                'angle': i * 18,  # 360/20
                'radius': 80 + (i % 3) * 20,
                'size': 2 + (i % 3),
                'speed': 1 + (i % 4) * 0.5,
                'alpha': 0.8 - (i % 4) * 0.15
            }
            self.particles.append(particle)
    
    def start_animation(self):
        """Start the notification animation"""
        if self.animation_running:
            return
        
        self.animation_running = True
        self.start_time = time.time()
        self.animation_thread = threading.Thread(target=self._animation_loop, daemon=True)
        self.animation_thread.start()
    
    def _animation_loop(self):
        """Main animation loop"""
        while self.animation_running:
            current_time = time.time()
            elapsed = current_time - self.start_time
            
            # Check if duration exceeded
            if elapsed >= self.duration:
                self._fade_out()
                break
            
            # Update animation
            self._update_animation(elapsed)
            self._draw_frame()
            
            time.sleep(0.033)  # ~30 FPS
    
    def _update_animation(self, elapsed: float):
        """Update animation properties"""
        self.pulse_phase = (elapsed * 3) % (2 * math.pi)
        
        # Update rings
        for ring in self.rings:
            ring['rotation'] = (ring['rotation'] + ring['speed']) % 360
        
        # Update particles
        for particle in self.particles:
            particle['angle'] = (particle['angle'] + particle['speed']) % 360
    
    def _draw_frame(self):
        """Draw a single animation frame"""
        if not self.canvas or not self.window:
            return

        try:
            # Check if window still exists
            self.window.winfo_exists()

            self.canvas.delete("all")

            # Draw energy rings
            self._draw_energy_rings()

            # Draw particles
            self._draw_particles()

            # Draw central core
            self._draw_core()

            # Draw message
            self._draw_message()

            # Update canvas
            self.canvas.update()

        except (tk.TclError, RuntimeError):
            # Window was destroyed or main thread issue
            self.animation_running = False
    
    def _draw_energy_rings(self):
        """Draw animated energy rings"""
        for i, ring in enumerate(self.rings):
            # Calculate pulsing effect
            pulse_factor = 1 + 0.2 * math.sin(self.pulse_phase + i * 0.5)
            radius = ring['radius'] * pulse_factor
            
            # Calculate alpha based on pulse
            alpha = ring['alpha'] * (0.7 + 0.3 * math.sin(self.pulse_phase + i * 0.3))
            
            # Draw ring segments for energy effect
            segments = 8
            segment_angle = 360 / segments
            
            for j in range(segments):
                if j % 2 == 0:  # Skip every other segment for broken ring effect
                    continue
                
                start_angle = ring['rotation'] + (j * segment_angle)
                extent = segment_angle * 0.7  # Gaps between segments
                
                color = self._adjust_color_alpha(self.color, alpha)
                
                self.canvas.create_arc(
                    self.center - radius, self.center - radius,
                    self.center + radius, self.center + radius,
                    start=start_angle, extent=extent,
                    outline=color, width=int(ring['width']),
                    style="arc"
                )
    
    def _draw_particles(self):
        """Draw energy particles"""
        for particle in self.particles:
            # Calculate position
            angle_rad = math.radians(particle['angle'])
            x = self.center + particle['radius'] * math.cos(angle_rad)
            y = self.center + particle['radius'] * math.sin(angle_rad)
            
            # Pulsing effect
            pulse = 1 + 0.3 * math.sin(self.pulse_phase * 2 + particle['angle'] * 0.1)
            size = particle['size'] * pulse
            
            # Color with alpha
            alpha = particle['alpha'] * (0.8 + 0.2 * math.sin(self.pulse_phase + particle['angle'] * 0.05))
            color = self._adjust_color_alpha(self.color, alpha)
            
            # Draw particle
            self.canvas.create_oval(
                x - size, y - size, x + size, y + size,
                fill=color, outline=color
            )
    
    def _draw_core(self):
        """Draw central energy core"""
        # Pulsing core
        pulse = 1 + 0.4 * math.sin(self.pulse_phase * 2)
        core_radius = 15 * pulse
        
        # Multiple layers for glow effect
        for i in range(5):
            radius = core_radius + (i * 3)
            alpha = 1.0 - (i * 0.15)
            color = self._adjust_color_alpha(self.color, alpha)
            
            self.canvas.create_oval(
                self.center - radius, self.center - radius,
                self.center + radius, self.center + radius,
                fill=color, outline=""
            )
    
    def _draw_message(self):
        """Draw the notification message"""
        # Background for text
        text_bg_radius = 40
        self.canvas.create_oval(
            self.center - text_bg_radius, self.center + 60,
            self.center + text_bg_radius, self.center + 100,
            fill="#000000", outline=self.color, width=2
        )
        
        # Message text
        self.canvas.create_text(
            self.center, self.center + 80,
            text=self.message,
            fill=self.color,
            font=("Segoe UI", 10, "bold"),
            width=150,
            justify="center"
        )
    
    def _adjust_color_alpha(self, color: str, alpha: float) -> str:
        """Adjust color transparency"""
        if color.startswith("#"):
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
            
            # Apply alpha
            r = int(r * alpha)
            g = int(g * alpha)
            b = int(b * alpha)
            
            return f"#{r:02x}{g:02x}{b:02x}"
        return color
    
    def _fade_out(self):
        """Fade out animation"""
        if not self.window:
            return
        
        try:
            for alpha in [0.8, 0.6, 0.4, 0.2, 0.0]:
                self.window.attributes('-alpha', alpha)
                time.sleep(0.1)
        except tk.TclError:
            pass
        
        self.close()
    
    def close(self):
        """Close the notification"""
        self.animation_running = False
        
        if self.window:
            try:
                self.window.destroy()
            except tk.TclError:
                pass
            self.window = None


class NotificationManager:
    """Manages visual notifications"""
    
    def __init__(self):
        self.active_notifications = []
    
    def show_notification(self, message: str, duration: float = 3.0, color: str = "#00D4FF"):
        """Show a J.A.R.V.I.S.-style notification"""
        # Close any existing notifications
        self.close_all()
        
        # Create new notification
        notification = JarvisNotification(message, duration, color)
        self.active_notifications.append(notification)
        
        # Auto-remove after duration
        def remove_notification():
            time.sleep(duration + 1)
            if notification in self.active_notifications:
                self.active_notifications.remove(notification)
        
        threading.Thread(target=remove_notification, daemon=True).start()
    
    def close_all(self):
        """Close all active notifications"""
        for notification in self.active_notifications[:]:
            notification.close()
            self.active_notifications.remove(notification)
