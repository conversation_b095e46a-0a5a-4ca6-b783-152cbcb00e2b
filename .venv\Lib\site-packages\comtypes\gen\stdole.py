from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    StdPicture, dispid, OLE_XPOS_HIMETRIC, OLE_YPOS_PIXELS, BSTR,
    COMMETHOD, OLE_ENABLEDEFAULTBOOL, OLE_COLOR, typelib_path, Color,
    OLE_YPOS_HIMETRIC, <PERSON><PERSON>, Gray, IDispatch, FONTBOLD, GUID,
    OLE_XSIZE_HIMETRIC, CoClass, DISPPROPERTY, _check_version, Font,
    Library, IEnumVARIANT, HRESULT, <PERSON>LE_CANCELBOOL, FONTSTRIKETHROUGH,
    VgaColor, OLE_YSIZE_CONTAINER, OLE_YSIZE_PIXELS, IPictureDisp,
    DISPPARAMS, FONTITALIC, <PERSON><PERSON>_OPTEXCLUSIVE, EXCEPINFO,
    F<PERSON><PERSON>NDER<PERSON>OR<PERSON>, De<PERSON>ult, <PERSON>LE_XPOS_CONTAINER, OLE_YSIZE_HIMETRIC,
    OLE_XSIZE_PIXELS, OLE_XSIZE_CONTAINER, IFontDisp, IPicture, _lcid,
    DISPMETHOD, StdFont, VARIANT_BOOL, FontEvents, OLE_YPOS_CONTAINER,
    Monochrome, Picture, FONTNAME, Checked, IUnknown, OLE_XPOS_PIXELS,
    Unchecked, OLE_HANDLE, IFontEventsDisp, FONTSIZE
)


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


__all__ = [
    'StdPicture', 'IPictureDisp', 'OLE_XPOS_HIMETRIC', 'FONTITALIC',
    'OLE_OPTEXCLUSIVE', 'OLE_YPOS_PIXELS', 'OLE_ENABLEDEFAULTBOOL',
    'FONTUNDERSCORE', 'Default', 'OLE_XPOS_CONTAINER', 'OLE_COLOR',
    'OLE_YSIZE_HIMETRIC', 'typelib_path', 'OLE_XSIZE_PIXELS', 'Color',
    'OLE_YPOS_HIMETRIC', 'IFont', 'OLE_XSIZE_CONTAINER', 'IFontDisp',
    'Gray', 'IPicture', 'FONTBOLD', 'StdFont', 'FONTSIZE',
    'FontEvents', 'OLE_YPOS_CONTAINER', 'Monochrome',
    'OLE_XSIZE_HIMETRIC', 'OLE_TRISTATE', 'Picture', 'Font',
    'FONTNAME', 'LoadPictureConstants', 'Library', 'Checked',
    'OLE_CANCELBOOL', 'OLE_XPOS_PIXELS', 'Unchecked',
    'FONTSTRIKETHROUGH', 'OLE_HANDLE', 'VgaColor', 'IFontEventsDisp',
    'OLE_YSIZE_CONTAINER', 'OLE_YSIZE_PIXELS'
]

