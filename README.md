# J.A.R.V.I.S. Pomodoro Timer

A premium, modern Pomodoro timer with J.A.R.V.I.S.-inspired aesthetics featuring cutting-edge UI design and advanced notification systems.

![J.A.R.V.I.S. Pomodoro Timer](https://img.shields.io/badge/Status-Ready-brightgreen) ![Python](https://img.shields.io/badge/Python-3.8+-blue) ![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey)

## ✨ Features

### 🎯 **True J.A.R.V.I.S. UI Experience**
- **Pulsing Circular Waveform**: Signature J.A.R.V.I.S. energy ripples and sonar-like glow effects
- **Hexagonal Elements**: Futuristic geometric patterns that rotate and pulse
- **Energy Particle Systems**: Dynamic particles with trails and orbital animations
- **HUD-Style Interface**: Corner brackets, status displays, and sci-fi typography
- **Responsive Animations**: Waveform reacts to timer state and voice notifications
- **Premium Dark Theme**: True black background with thunder blue (#00D4FF) accents

### 🤖 **J.A.R.V.I.S.-Style Visual Notifications**
- Animated circular overlays with energy effects
- Appears in bottom-right corner of screen
- Glowing particles and rotating energy rings
- Customizable colors for different session types

### 🔊 **Advanced Voice Notifications**
- Text-to-speech with customizable messages
- Different voices and speech rates
- Session-specific announcements
- Fully customizable notification text

### 🔄 **System Integration**
- System tray integration with context menu
- Persistent background operation
- Works on top of any application
- Minimize to tray functionality

### ⚙️ **Complete Customization**
- Timer durations (work, short break, long break)
- Custom voice messages for all session types
- UI themes and color schemes
- Audio and visual notification preferences
- Auto-start options for sessions

### 🎨 **Advanced Visual Effects**
- **Pulsing Waveform**: Central energy core that pulses with timer activity
- **Energy Ripples**: Expanding circular waves that respond to voice and timer state
- **Particle Trails**: Energy particles with glowing trails orbiting the core
- **Hexagonal Geometry**: Rotating hexagonal elements for authentic J.A.R.V.I.S. feel
- **Dynamic Lighting**: Alpha-blended glow effects and energy arcs
- **Synthetic Audio**: J.A.R.V.I.S.-style generated sound effects

## 🚀 Installation

### Prerequisites
- Python 3.8 or higher
- Windows OS (for full system integration features)

### Setup
1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd "Pomodoro Timer"
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python main.py
   ```

## 🎮 Usage

### Basic Operation
1. **Start the Timer**: Click the "▶ START" button to begin a work session
2. **Pause/Resume**: Use "⏸ PAUSE" to pause and "▶ RESUME" to continue
3. **Stop Timer**: Click "⏹ STOP" to reset the current session
4. **Skip Session**: Use "⏭ SKIP" to move to the next session immediately

### Settings Configuration
1. Click the "⚙ SETTINGS" button in the main window
2. Configure timer durations, voice messages, and UI preferences
3. Save settings to apply changes

### System Tray
- The application minimizes to the system tray
- Right-click the tray icon for quick controls
- Double-click to show/hide the main window

### Keyboard Shortcuts
- The application supports standard window controls
- Use Alt+F4 to close (minimizes to tray if enabled)

## 🔧 Configuration

### Timer Settings
- **Work Duration**: Default 25 minutes
- **Short Break**: Default 5 minutes
- **Long Break**: Default 15 minutes
- **Sessions until Long Break**: Default 4 sessions

### Voice Messages
Customize messages for each session transition:
- Work session start/complete
- Short break start/complete
- Long break start/complete

### UI Customization
- Theme colors (default: thunder blue #00D4FF)
- Window size and behavior
- Animation settings
- Always-on-top preference

## 🧪 Testing

Run the test suite to verify functionality:
```bash
python test_app.py
```

## 📁 Project Structure

```
Pomodoro Timer/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── test_app.py            # Test suite
├── README.md              # This file
└── src/
    ├── app.py             # Main application class
    ├── timer.py           # Core timer logic
    ├── settings.py        # Settings management
    ├── ui/                # User interface components
    │   ├── main_window.py # Main application window
    │   └── settings_window.py # Settings configuration
    ├── notifications/     # Notification systems
    │   ├── visual_notification.py # J.A.R.V.I.S. visual effects
    │   └── voice_notification.py  # TTS voice system
    ├── audio/            # Sound effects and audio
    │   └── sound_effects.py # Synthetic audio generation
    └── system/           # System integration
        └── tray_manager.py # System tray and window management
```

## 🛠 Tech Stack

- **GUI Framework**: CustomTkinter for modern, dark-themed interface
- **Audio Processing**: Pygame + NumPy for synthetic sound generation
- **Text-to-Speech**: pyttsx3 for voice notifications
- **Visual Effects**: Tkinter Canvas with custom animations
- **System Integration**: pystray for system tray, pywin32 for Windows features
- **Image Processing**: Pillow for icon generation and image handling

## 🎯 Requirements

- **Python**: 3.8 or higher
- **Operating System**: Windows (recommended for full features)
- **Memory**: Minimal requirements (~50MB RAM)
- **Audio**: Sound card for voice and audio notifications

## 🤝 Contributing

This is a complete, production-ready Pomodoro timer. Feel free to:
- Report bugs or issues
- Suggest new features
- Submit improvements
- Create themes or customizations

## 📄 License

This project is provided as-is for educational and personal use.

---

**Enjoy your productive work sessions with J.A.R.V.I.S.! 🚀**
