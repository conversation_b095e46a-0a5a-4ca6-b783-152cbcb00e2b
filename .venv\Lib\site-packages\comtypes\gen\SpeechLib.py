from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    DISPID_SLWsCount, SAFTADPCM_8kHzStereo, SASPause,
    SPAUDIOBUFFERINFO, DISPID_SRCEBookmark, SVSFNLPMask, COMMETHOD,
    SVF_None, SAFTGSM610_11kHzMono, ISpSerializeState, ISpShortcut,
    SpSharedRecoContext, SDA_One_Trailing_Space, SVSFIsFilename,
    ISpRecognizer2, SpeechTokenKeyUI, DISPID_SAVolume,
    DISPID_SVEventInterests, SPFM_CREATE, SPPS_Unknown,
    DISPID_SVStatus, SAFT48kHz8BitMono, DISPID_SRCPause, G<PERSON>D,
    eLEXTYPE_PRIVATE11, DISPID_SRCC<PERSON>Gram<PERSON>,
    Speech<PERSON>ropertyNormalConfidenceThreshold, DISPID_SCSBaseStream,
    SPPS_Noun, DISPID_SOTId, DISPID_SPRuleEngineConfidence,
    SDTLexicalForm, SpeechCategoryAudioOut,
    DISPID_SLGetGenerationChange, SpeechVoiceCategoryTTSRate,
    SPEI_ACTIVE_CATEGORY_CHANGED, SGRSTTDictation,
    SAFTNoAssignedFormat, SPINTERFERENCE_TOOSLOW,
    DISPID_SWFEExtraData, DISPID_SPEAudioStreamOffset,
    SPCT_SUB_DICTATION, SVP_4, STCInprocHandler, ISpProperties,
    SLTApp, __MIDL___MIDL_itf_sapi_0000_0020_0002, SpShortcut,
    DISPID_SGRsCommitAndSave, DISPID_SRAudioInputStream, SP_VISEME_7,
    Speech_StreamPos_Asap, SPPS_LMA, SPEI_WORD_BOUNDARY,
    ISpResourceManager, SPBO_PAUSE, SVSFPersistXML, SP_VISEME_12,
    SPINTERFERENCE_TOOLOUD, SRTAutopause, DISPID_SABIBufferSize,
    SSSPTRelativeToCurrentPosition, SAFTGSM610_44kHzMono,
    DISPID_SPPFirstElement, SpMemoryStream, DISPID_SRCEStartStream,
    DISPID_SPELexicalForm, SpPhoneConverter, DISPID_SGRSTPropertyName,
    DISPID_SDKGetlongValue, SPSMF_SRGS_SAPIPROPERTIES,
    SpResourceManager, SSFMOpenReadWrite,
    ISpPhoneticAlphabetConverter, DISPID_SPEPronunciation,
    SpeechCategoryRecoProfiles, SASStop, SAFT24kHz16BitMono,
    DISPID_SDKGetStringValue, DISPID_SRCEHypothesis,
    DISPID_SPIReplacements, SAFTCCITT_uLaw_8kHzStereo, SECFDefault,
    SPSERIALIZEDPHRASE, SRTSMLTimeout, SPXRO_SML, DISPID_SGRsItem,
    SVEAllEvents, SECFNoSpecialChars, DISPID_SVAudioOutputStream,
    SVP_16, DISPID_SPIRetainedSizeBytes, SpeechAudioVolume,
    SP_VISEME_14, SDA_No_Trailing_Space, SPPS_Modifier,
    SPEI_SENTENCE_BOUNDARY, DISPID_SRGReset, SDTProperty,
    DISPID_SRCERecognitionForOtherContext, ISpeechPhraseRule,
    DISPID_SRSCurrentStreamNumber, DISPID_SVVoice, SPPS_RESERVED2,
    SpInprocRecognizer, SDTAudio, DISPID_SOTsItem,
    DISPID_SASNonBlockingIO, SVEViseme, SLTUser, SAFT24kHz16BitStereo,
    SpeechPropertyAdaptationOn, DISPID_SLWs_NewEnum,
    DISPID_SGRSTransitions, SPVOICESTATUS, SREPhraseStart,
    DISPID_SVSInputSentenceLength, DISPID_SRGId,
    DISPID_SRCESoundStart, DISPID_SRRSaveToMemory,
    SAFT48kHz8BitStereo, SPSHORTCUTPAIR, ISpPhoneConverter, SBONone,
    SPEI_START_SR_STREAM, SpPhraseInfoBuilder, SP_VISEME_19,
    SGPronounciation, DISPID_SPRFirstElement,
    SAFTCCITT_uLaw_44kHzStereo, SAFT32kHz16BitStereo,
    SpeechRecoProfileProperties, DISPID_SOTRemoveStorageFileName,
    SINoSignal, SREStateChange, SRTReSent, DISPID_SRCBookmark,
    SPPHRASEELEMENT, SPRECOGNIZERSTATUS, ISpeechWaveFormatEx,
    SPCT_DICTATION, SpeechCategoryPhoneConverters,
    ISpeechMemoryStream, SFTInput,
    SPINTERFERENCE_LATENCY_TRUNCATE_END, ISpAudio,
    DISPID_SPEEngineConfidence, eLEXTYPE_PRIVATE18,
    DISPID_SRCERecognition, SP_VISEME_5, DISPID_SDKDeleteKey,
    SPWORDLIST, SWTDeleted, SPSMF_SRGS_SEMANTICINTERPRETATION_W3C,
    SRTStandard, SPDKL_LocalMachine, DISPID_SRSClsidEngine,
    SVSFParseSsml, SPSMF_SRGS_SEMANTICINTERPRETATION_MS,
    SpeechAudioFormatGUIDText, ISpeechFileStream, SVEPhoneme,
    DISPID_SLPs_NewEnum, SPRST_ACTIVE_ALWAYS, SpeechAudioProperties,
    eLEXTYPE_RESERVED9, DISPID_SVEVoiceChange,
    ISpeechGrammarRuleStateTransition, DISPID_SLWWord, SRSEIsSpeaking,
    SAFT8kHz16BitStereo, SpeechTokenKeyAttributes, SPSModifier,
    DISPID_SOTRemove, DISPID_SGRAttributes, STCLocalServer,
    DISPID_SLRemovePronunciationByPhoneIds, SRERequestUI,
    DISPID_SRCSetAdaptationData, DISPID_SGRSTWeight, dispid,
    DISPID_SRCEPhraseStart, DISPID_SPRuleId, DISPID_SRCVoice, SDTAll,
    SVP_15, eLEXTYPE_PRIVATE3, DISPID_SVSInputWordPosition,
    eWORDTYPE_ADDED, typelib_path, SPWT_DISPLAY, SPAR_High,
    eLEXTYPE_USER, SAFT22kHz16BitStereo, SpSharedRecognizer,
    eLEXTYPE_PRIVATE17, SPEI_VISEME, SPTEXTSELECTIONINFO, SGLexical,
    SpeechVoiceSkipTypeSentence, SVSFNLPSpeakPunc,
    SAFTCCITT_ALaw_11kHzStereo, SPINTERFERENCE_TOOQUIET,
    DISPID_SVSCurrentStreamNumber, DISPID_SRGetFormat, SRSInactive,
    SGRSTTWord, DISPID_SOTs_NewEnum, DISPID_SOTCGetDataKey,
    SECFEmulateResult, DISPID_SVAlertBoundary, DISPID_SGRAddState,
    SPEI_SR_RETAINEDAUDIO, SPVPRI_OVER, SVSFVoiceMask, SPGS_EXCLUSIVE,
    SPCS_ENABLED, SECLowConfidence, DISPID_SBSFormat, SPSHT_Unknown,
    SAFT32kHz8BitStereo, SAFTCCITT_uLaw_11kHzMono,
    DISPID_SRRDiscardResultInfo, SpCompressedLexicon,
    eLEXTYPE_PRIVATE1, ISpeechAudioStatus, ISpObjectTokenCategory,
    DISPID_SPANumberOfElementsInResult,
    DISPID_SVSyncronousSpeakTimeout, DISPID_SVIsUISupported,
    DISPID_SWFEBitsPerSample, SPSMF_UPS, SPWORDPRONUNCIATION,
    _LARGE_INTEGER, DISPID_SRGCmdLoadFromObject, SPBO_TIME_UNITS,
    DISPID_SVEBookmark, SVSFPurgeBeforeSpeak, IServiceProvider,
    DISPID_SMSGetData, SVP_20, DISPID_SGRSTNextState,
    SAFT22kHz16BitMono, DISPID_SPCIdToPhone, SAFT11kHz8BitMono,
    DISPID_SPPBRestorePhraseFromMemory, DISPID_SPRules_NewEnum,
    ISequentialStream, SPSHT_OTHER, SVP_7, DISPID_SGRAddResource,
    SAFTGSM610_22kHzMono, DISPID_SPPNumberOfElements, SPGS_ENABLED,
    SP_VISEME_6, DISPID_SPIStartTime, ISpeechPhraseRules, SFTSREngine,
    SPAR_Unknown, SPEI_MAX_TTS, SDKLLocalMachine,
    DISPID_SGRSTPropertyValue, SPRECOCONTEXTSTATUS,
    DISPID_SVSLastStreamNumberQueued, SVP_13, DISPID_SGRsAdd,
    DISPID_SPAsCount, SPINTERFERENCE_NOSIGNAL,
    SPRS_ACTIVE_USER_DELIMITED, SREFalseRecognition,
    DISPID_SVAudioOutput, DISPID_SRSetPropertyString,
    SPRST_INACTIVE_WITH_PURGE, DISPID_SRSCurrentStreamPosition,
    ISpeechPhraseElement, DISPID_SGRsDynamic, DISPID_SLPPhoneIds,
    SpeechPropertyLowConfidenceThreshold, DISPID_SPRuleParent,
    DISPID_SRGCmdSetRuleState, ISpRecognizer3, ISpeechRecoGrammar,
    eLEXTYPE_PRIVATE20, VARIANT, DISPID_SOTCDefault, STCRemoteServer,
    ISpeechLexiconWords, DISPID_SVGetAudioOutputs,
    DISPID_SWFEAvgBytesPerSec, SPSNoun, SPEI_SR_PRIVATE,
    DISPID_SOTCId, DISPID_SVPriority, DISPID_SRCVoicePurgeEvent,
    tagSTATSTG, SPINTERFERENCE_TOOFAST,
    SPINTERFERENCE_LATENCY_WARNING, DISPID_SRCEventInterests,
    SP_VISEME_4, DISPID_SGRsFindRule, wireHWND,
    DISPID_SRCEEnginePrivate, DISPID_SPRsItem, tagSPPROPERTYINFO,
    DISPID_SVEPhoneme, SPEI_SOUND_END, ISpeechAudio,
    DISPID_SPEDisplayText, DISPID_SPRulesItem,
    ISpeechPhraseInfoBuilder, DISPID_SASetState, DISPID_SPRulesCount,
    DISPID_SOTsCount, SPRULE, SPRST_NUM_STATES, ISpeechPhraseElements,
    DISPID_SRGSetWordSequenceData, SGSExclusive, SAFT44kHz16BitStereo,
    ISpStreamFormatConverter, SPSLMA, SSTTTextBuffer, SPEI_ADAPTATION,
    DISPID_SPERequiredConfidence, DISPID_SRGetRecognizers,
    DISPID_SRAudioInput, DISPID_SVPause,
    DISPID_SPIAudioStreamPosition, DISPID_SVEViseme,
    SpeechAudioFormatGUIDWave, SAFTCCITT_ALaw_8kHzStereo,
    SPPS_RESERVED4, SPEVENTSOURCEINFO, ISpRecognizer, SVSFParseMask,
    DISPID_SRCEFalseRecognition, DISPID_SPEsItem, SVPNormal,
    SPEI_RECO_OTHER_CONTEXT, SITooFast, SAFTCCITT_uLaw_22kHzMono,
    SPSVerb, SVP_10, DISPID_SVEStreamStart, SpStreamFormatConverter,
    ISpeechXMLRecoResult, SAFT32kHz16BitMono, SPSInterjection,
    SGRSTTEpsilon, eLEXTYPE_PRIVATE12, ISpeechPhraseReplacements,
    ISpeechObjectTokenCategory, DISPID_SPRuleConfidence,
    DISPID_SGRSTsItem, ISpeechBaseStream, SPEVENT,
    SpeechEngineProperties, SSFMOpenForRead, DISPID_SGRClear,
    DISPIDSPTSI_ActiveLength, DISPID_SMSALineId, DISPID_SVSpeakStream,
    __MIDL_IWinTypes_0009, SINoise, DISPID_SRGetPropertyNumber,
    DISPID_SVResume, ISpeechCustomStream, DISPID_SGRName,
    DISPID_SVGetProfiles, ISpeechRecognizer, SAFTADPCM_11kHzMono,
    DISPID_SRSSupportedLanguages, DISPID_SADefaultFormat,
    DISPID_SRRAlternates, SRSActive, DISPID_SPARecoResult,
    DISPID_SVEStreamEnd, SRTEmulated, DISPIDSPTSI_SelectionLength,
    SPEI_FALSE_RECOGNITION, SRSInactiveWithPurge, SpNotifyTranslator,
    ISpeechPhraseProperties, DISPID_SRRTStreamTime,
    DISPID_SPERetainedSizeBytes, SSFMCreateForWrite,
    SAFTTrueSpeech_8kHz1BitMono, SVEBookmark, DISPID_SVEAudioLevel,
    DISPID_SOTCEnumerateTokens, DISPID_SPRuleNumberOfElements,
    ISpeechPhoneConverter, ISpeechGrammarRuleState,
    DISPID_SRRAudioFormat, SASClosed, SPSHT_NotOverriden,
    SRAInterpreter, ISpeechRecoResultDispatch, DISPID_SRRRecoContext,
    SVP_14, DISPID_SOTGetDescription, SVP_2, eWORDTYPE_DELETED,
    SpeechRegistryUserRoot, SLODynamic, SVPOver,
    SAFTCCITT_ALaw_11kHzMono, DISPID_SGRSAddRuleTransition,
    DISPID_SVEEnginePrivate, eLEXTYPE_APP, SVEVoiceChange,
    SECFIgnoreWidth, DISPID_SRDisplayUI, DISPID_SOTDisplayUI,
    SWTAdded, SREInterference, SSTTWildcard, SP_VISEME_18,
    DISPID_SVSpeak, DISPID_SPAStartElementInResult, STSF_FlagCreate,
    DISPID_SPPEngineConfidence, DISPID_SLGetWords,
    DISPID_SRCRetainedAudio, ISpeechPhraseAlternate,
    SPWT_LEXICAL_NO_SPECIAL_CHARS, DISPID_SGRSTs_NewEnum,
    DISPID_SLWPronunciations, DISPID_SDKSetStringValue,
    DISPID_SRRGetXMLErrorInfo, SPCT_SLEEP, SVF_Stressed, SP_VISEME_10,
    eLEXTYPE_PRIVATE7, DISPID_SRGDictationLoad,
    DISPID_SPEAudioTimeOffset, DISPID_SRCERecognizerStateChange,
    SAFT16kHz16BitMono, DISPID_SGRSRule, ISpNotifyTranslator,
    SpeechCategoryRecognizers, Library, DISPID_SPRuleFirstElement,
    SpObjectTokenCategory, SPEI_UNDEFINED, SGDSActiveUserDelimited,
    SPAS_RUN, SVSFParseSapi, STCInprocServer,
    DISPIDSPTSI_SelectionOffset, SpObjectToken, DISPID_SRRecognizer,
    DISPID_SMSAMMHandle, DISPID_SRCEPropertyStringChange,
    SDTDisplayText, SWPKnownWordPronounceable, SPWF_SRENGINE,
    DISPID_SRIsShared, ISpeechLexiconWord, eLEXTYPE_PRIVATE16,
    eLEXTYPE_RESERVED4, SDA_Two_Trailing_Spaces, SP_VISEME_8,
    SVSFIsXML, SAFTADPCM_22kHzMono, ISpMMSysAudio,
    SPINTERFERENCE_NONE, SAFTCCITT_uLaw_22kHzStereo,
    SAFTADPCM_44kHzStereo, SITooQuiet, SPDKL_CurrentConfig,
    DISPID_SRGCmdLoadFromProprietaryGrammar, SAFTADPCM_22kHzStereo,
    SPEI_RECO_STATE_CHANGE, SVP_5, ISpPhrase, Speech_Default_Weight,
    DISPID_SRCEPropertyNumberChange, ISpStream, SPPHRASE,
    SAFTADPCM_11kHzStereo, DISPID_SPCLangId, SRERecoOtherContext,
    SAFT16kHz16BitStereo, DISPID_SPPs_NewEnum, DISPID_SRState, BSTR,
    DISPID_SLPLangId, SPVPRI_NORMAL, ISpeechAudioFormat,
    SSTTDictation, SAFTCCITT_ALaw_22kHzStereo, SpWaveFormatEx,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet,
    DISPID_SRAllowAudioInputFormatChangesOnNextSet, SGSDisabled,
    SpeechDictationTopicSpelling, SPPS_SuppressWord,
    DISPID_SOTDataKey, SpeechCategoryVoices, SPEI_PROPERTY_NUM_CHANGE,
    eLEXTYPE_PRIVATE5, ISpRecoContext, SECNormalConfidence,
    DISPID_SLPsItem, DISPID_SPPChildren, SDKLDefaultLocation,
    SPWORDPRONUNCIATIONLIST, SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE,
    DISPID_SPRs_NewEnum, ISpEventSink, DISPID_SRGCommit,
    DISPID_SASFreeBufferSpace, SAFT11kHz16BitMono, SPEI_RESERVED1,
    SPRECORESULTTIMES, ISpeechPhraseProperty,
    DISPID_SRCCmdMaxAlternates, SAFT16kHz8BitStereo,
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN, DISPID_SPRNumberOfElements,
    SPEI_PHRASE_START, DISPID_SRRTTickCount, SpeechAddRemoveWord,
    SPDKL_CurrentUser, SPLO_STATIC, DISPID_SVGetVoices,
    DISPID_SVESentenceBoundary, SAFT44kHz16BitMono, DISPID_SPRsCount,
    _ISpeechRecoContextEvents, SPPHRASERULE, SVP_3, SRADynamic,
    DISPID_SPEActualConfidence, UINT_PTR,
    DISPID_SRCRetainedAudioFormat, SPEI_SOUND_START, DISPID_SGRSTText,
    DISPID_SRStatus, SP_VISEME_3, DISPID_SRGDictationUnload,
    SREStreamEnd, SpeechCategoryAppLexicons, DISPID_SVSRunningState,
    DISPID_SPIGetDisplayAttributes, SPEI_TTS_PRIVATE,
    SAFTCCITT_uLaw_8kHzMono, SECFIgnoreKanaType, DISPID_SPEs_NewEnum,
    eLEXTYPE_LETTERTOSOUND, eLEXTYPE_PRIVATE2, SDTRule,
    DISPID_SLPsCount, ISpXMLRecoResult, SpeechCategoryAudioIn,
    DISPID_SREmulateRecognition, DISPIDSPTSI_ActiveOffset,
    SpMMAudioOut, eLEXTYPE_RESERVED10, DISPID_SRRSpeakAudio,
    SPSERIALIZEDRESULT, SPEI_INTERFERENCE, DISPID_SVEWord,
    ISpeechVoice, DISPID_SRGCmdLoadFromFile, ISpRecoGrammar,
    DISPID_SLAddPronunciationByPhoneIds, SVF_Emphasis,
    DISPID_SPIGetText, ISpDataKey, SVSFIsNotXML, SPEI_TTS_AUDIO_LEVEL,
    DISPID_SVWaitUntilDone, SREBookmark,
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE, DISPID_SPPId, SRAORetainAudio,
    DISPID_SOTGetAttribute, DISPID_SPIElements, ISpRecoCategory,
    SPEI_SR_AUDIO_LEVEL, SVP_21, _RemotableHandle,
    DISPID_SLGetPronunciations, DISPID_SRGCmdLoadFromMemory,
    SAFT16kHz8BitMono, eLEXTYPE_RESERVED8,
    SpeechRegistryLocalMachineRoot, DISPID_SGRsCount,
    SVEStartInputStream, SPBINARYGRAMMAR, _lcid, SPGS_DISABLED,
    SAFTDefault, eLEXTYPE_USER_SHORTCUT, SAFT8kHz8BitMono,
    SPPROPERTYINFO, SAFT11kHz8BitStereo, SPAR_Medium, SPWT_LEXICAL,
    SPAS_PAUSE, SPEI_END_SR_STREAM, DISPID_SPPName,
    DISPID_SRGRecoContext, DISPID_SVSVisemeId, SPPS_RESERVED1,
    SPPS_Verb, SPXRO_Alternates_SML, DISPID_SAFGetWaveFormatEx,
    DISPID_SDKGetBinaryValue, DISPID_SRGIsPronounceable,
    SpeechPropertyComplexResponseSpeed, SLOStatic, DISPID_SRCResume,
    SP_VISEME_20, DISPID_SRGCmdLoadFromResource, DISPID_SLWType,
    SDTPronunciation, SPRST_ACTIVE, DISPID_SPIEngineId, SVP_12,
    SPRS_INACTIVE, SPINTERFERENCE_NOISE, eLEXTYPE_PRIVATE14,
    ISpeechObjectTokens, SAFT24kHz8BitStereo, DISPID_SRCEInterference,
    SGDSActiveWithAutoPause, SPPHRASEPROPERTY, eLEXTYPE_PRIVATE8,
    DISPID_SRSAudioStatus, ISpeechRecoContext, ISpeechMMSysAudio,
    SPAR_Low, SPEI_TTS_BOOKMARK, SPEI_PROPERTY_STRING_CHANGE,
    ISpNotifySink, SpeechPropertyResponseSpeed, IEnumSpObjectTokens,
    DISPID_SRCreateRecoContext, DISPID_SRCEAdaptation,
    DISPID_SVSLastBookmarkId, SPSFunction, SAFTNonStandardFormat,
    DISPID_SPPsItem, DISPID_SPPsCount, DISPID_SLWsItem,
    ISpeechRecoResult2, ISpRecoGrammar2,
    DISPID_SRAllowVoiceFormatMatchingOnNextSet, SPFM_NUM_MODES,
    SSFMCreate, DISPID_SPACommit, ISpeechObjectToken, SP_VISEME_15,
    SP_VISEME_16, DISPID_SRCESoundEnd, DISPID_SAStatus,
    SVSFParseAutodetect, SPEI_RESERVED5, SPSMF_SAPI_PROPERTIES,
    SPCT_SUB_COMMAND, DISPID_SRCRequestedUIType,
    SpeechGrammarTagDictation, SpMMAudioEnum, SRCS_Enabled,
    DISPID_SPAs_NewEnum, SAFTADPCM_8kHzMono, SRESoundEnd,
    ISpNotifySource, DISPID_SGRSTPropertyId, SGDisplay,
    SPRS_ACTIVE_WITH_AUTO_PAUSE, SRARoot, SRTExtendableParse,
    ISpeechGrammarRules, SPEI_HYPOTHESIS, DISPID_SPIGrammarId,
    DISPID_SFSClose, SpTextSelectionInformation,
    DISPID_SLRemovePronunciation, SPPS_Interjection,
    ISpeechRecognizerStatus, SGSEnabled, SAFTExtendedAudioFormat,
    LONG_PTR, DISPID_SPEDisplayAttributes, DISPID_SRGetPropertyString,
    ISpeechLexicon, SPAO_RETAIN_AUDIO, ISpeechRecoResult,
    DISPID_SOTCreateInstance, DISPID_SPIAudioSizeTime,
    DISPID_SRSetPropertyNumber, SpeechPropertyResourceUsage,
    SAFTCCITT_ALaw_44kHzMono, _FILETIME, SPCS_DISABLED,
    DISPID_SAFSetWaveFormatEx, SRESoundStart, SDTAlternates,
    ULONG_PTR, DISPID_SVSLastBookmark, DISPID_SLPType,
    DISPID_SASState, SAFT12kHz16BitMono, SpPhoneticAlphabetConverter,
    DISPID_SVSInputWordLength, DISPID_SGRSAddWordTransition,
    ISpStreamFormat, DISPID_SDKSetLongValue, SPLO_DYNAMIC,
    ISpEventSource, SVEAudioLevel, DISPID_SLAddPronunciation,
    SPWT_PRONUNCIATION, ISpObjectToken, SRAONone, SPAS_CLOSED,
    DISPID_SPAsItem, SPEI_RESERVED2, SPEI_MIN_SR, DISPID_SPPParent,
    DISPMETHOD, SAFT32kHz8BitMono, DISPID_SVSkip, DISPID_SRGRules,
    SAFT22kHz8BitStereo, VARIANT_BOOL, eLEXTYPE_PRIVATE4, SVP_18,
    SREPropertyNumChange, STSF_AppData, eLEXTYPE_PRIVATE10,
    DISPID_SPPConfidence, DISPID_SVSLastResult,
    DISPID_SRRTOffsetFromStart, SpMMAudioIn, SVP_9, SECHighConfidence,
    WAVEFORMATEX, WSTRING, eLEXTYPE_PRIVATE15, SRATopLevel,
    SWPUnknownWordPronounceable, SPFM_OPEN_READONLY,
    tagSPTEXTSELECTIONINFO, eLEXTYPE_PRIVATE13, DISPID_SRCEEndStream,
    SPWF_INPUT, ISpeechPhraseInfo, DISPID_SPILanguageId, SVEPrivate,
    SAFT8kHz16BitMono, SVSFDefault, SREAllEvents, DISPID_SBSSeek,
    DISPID_SRCERequestUI, SPEI_RECOGNITION, DISPID_SWFESamplesPerSec,
    SP_VISEME_0, DISPID_SPIProperties, SSSPTRelativeToEnd,
    SpeechMicTraining, DISPID_SOTGetStorageFileName,
    STSF_LocalAppData, CoClass, _check_version, SAFT12kHz16BitStereo,
    DISPID_SRRPhraseInfo, SDKLCurrentUser, DISPID_SABufferNotifySize,
    DISPID_SRProfile, DISPID_SGRInitialState, Speech_Max_Word_Length,
    SpInProcRecoContext, SP_VISEME_1, IStream, DISPID_SBSWrite,
    SpStream, IInternetSecurityManager, SP_VISEME_11, SDTReplacement,
    SAFT22kHz8BitMono, DISPID_SPIRule, DISPID_SRRTLength,
    Speech_Max_Pron_Length, DISPID_SPEsCount, SINone, SVP_6,
    DISPID_SPRuleName, SPAO_NONE, SBOPause, ISpRecoResult,
    SVESentenceBoundary, SPEI_RESERVED6, SPSSuppressWord,
    SPSHORTCUTPAIRLIST, ISpeechGrammarRule, ISpeechAudioBufferInfo,
    SVEEndInputStream, SPPS_Function, DISPID_SAFType,
    DISPID_SPRDisplayAttributes, ISpeechGrammarRuleStateTransitions,
    eLEXTYPE_RESERVED6, SREPropertyStringChange, SPEI_MAX_SR,
    SGRSTTTextBuffer, SITooLoud, DISPID_SRGDictationSetState,
    DISPID_SWFEChannels, SpeechPropertyHighConfidenceThreshold,
    SpLexicon, SAFT44kHz8BitStereo, SWPUnknownWordUnpronounceable,
    DISPID_SVSPhonemeId, DISPID_SGRSTType, SSSPTRelativeToStart,
    STSF_CommonAppData, DISPID_SWFEBlockAlign, ISpObjectWithToken,
    SpCustomStream, SPRS_ACTIVE, DISPID_SOTSetId, DISPID_SOTCSetId,
    SPPS_NotOverriden, SPEI_MIN_TTS, SpeechTokenIdUserLexicon,
    DISPID_SPERetainedStreamOffset, DISPID_SFSOpen, DISPID_SRCState,
    DISPID_SPISaveToMemory, SRERecognition, SPBO_NONE,
    SPDKL_DefaultLocation, DISPID_SASCurrentSeekPosition,
    DISPID_SVSInputSentencePosition, SP_VISEME_9,
    DISPID_SOTIsUISupported, DISPID_SMSSetData, SpeechUserTraining,
    SVP_19, DISPID_SRRTimes, ISpVoice, DISPID_SRRSetTextFeedback,
    SPWORD, DISPID_SPCPhoneToId, DISPID_SRGCmdSetRuleIdState,
    SPPS_RESERVED3, SPAS_STOP, SAFTCCITT_uLaw_44kHzMono,
    DISPID_SABIMinNotification, SpeechGrammarTagUnlimitedDictation,
    DISPID_SWFEFormatTag, SAFT48kHz16BitStereo,
    SAFTCCITT_ALaw_8kHzMono, SITooSlow, DISPID_SBSRead,
    DISPID_SOTCategory, SREAdaptation, SVSFlagsAsync,
    DISPID_SLWLangId, ISpLexicon, ISpeechLexiconPronunciations,
    DISPID_SDKEnumValues, SpUnCompressedLexicon,
    DISPID_SVSpeakCompleteEvent, SRAImport, SGDSInactive,
    DISPID_SRGState, DISPID_SRIsUISupported,
    DISPID_SRGSetTextSelection, SAFT24kHz8BitMono, ISpeechVoiceStatus,
    SVP_1, SP_VISEME_2, DISPID_SPAPhraseInfo, DISPID_SLPSymbolic,
    SRSEDone, ISpeechPhraseAlternates, DISPID_SVGetAudioInputs,
    DISPID_SPIAudioSizeBytes, ISpeechPhraseReplacement,
    ISpeechResourceLoader, ISpeechTextSelectionInformation,
    DISPID_SGRsCommit, DISPID_SDKOpenKey, SRSActiveAlways, SpVoice,
    SPEI_RESERVED3, ISpeechRecoResultTimes, DISPID_SPRText,
    SpNullPhoneConverter, DISPID_SMSADeviceId, SAFTGSM610_8kHzMono,
    STCAll, SGRSTTRule, HRESULT, DISPID_SABIEventBias,
    SVEWordBoundary, SPPS_Noncontent, DISPID_SGRId,
    SAFT11kHz16BitStereo, DISPID_SPEAudioSizeBytes,
    SAFT48kHz16BitMono, SPEI_SR_BOOKMARK, ISpPhraseAlt,
    SAFTCCITT_ALaw_44kHzStereo, __MIDL___MIDL_itf_sapi_0000_0020_0001,
    ISpRecoContext2, DISPID_SGRSTsCount, SREHypothesis, SPVPRI_ALERT,
    SVP_0, DISPID_SRCCreateResultFromMemory, SPEI_VOICE_CHANGE,
    DISPID_SDKSetBinaryValue, _ISpeechVoiceEvents, SPSUnknown,
    SGRSTTWildcard, DISPID_SPIEnginePrivateData, DISPID_SAEventHandle,
    SP_VISEME_17, DISPID_SVDisplayUI, DISPID_SRSNumberOfActiveRules,
    SPEI_START_INPUT_STREAM, SGLexicalNoSpecialChars,
    DISPID_SDKCreateKey, ISpeechLexiconPronunciation,
    SDA_Consume_Leading_Spaces, DISPID_SGRSAddSpecialTransition,
    ISpeechDataKey, SP_VISEME_21, DISPID_SVVolume,
    DISPID_SRCRecognizer, SVP_11, DISPID_SASCurrentDevicePosition,
    DISPID_SPEAudioSizeTime, SpFileStream, _ULARGE_INTEGER,
    SpeechGrammarTagWildcard, SPBO_AHEAD, SAFTADPCM_44kHzMono,
    SDKLCurrentConfig, eLEXTYPE_PRIVATE9, SREPrivate, SVSFUnusedFlags,
    SPSHT_EMAIL, DISPID_SABufferInfo, SpAudioFormat, helpstring,
    DISPID_SLPPartOfSpeech, SPPHRASEREPLACEMENT, DISPID_SGRSTRule,
    DISPID_SOTMatchesAttributes, SPWP_KNOWN_WORD_PRONOUNCEABLE,
    SRAExport, SPEI_REQUEST_UI, SRADefaultToActive,
    eLEXTYPE_RESERVED7, SAFT8kHz8BitStereo, SPRST_INACTIVE,
    ISpGrammarBuilder, DISPID_SDKEnumKeys, DISPID_SLGenerationId,
    eLEXTYPE_PRIVATE6, SPEI_END_INPUT_STREAM, SREAudioLevel,
    SPSEMANTICERRORINFO, SECFIgnoreCase, SAFT12kHz8BitMono, SVP_8,
    IInternetSecurityMgrSite, SASRun, eLEXTYPE_MORPHOLOGY,
    SpeechAllElements, SREStreamStart, SPFM_CREATE_ALWAYS,
    SPCT_COMMAND, SRCS_Disabled, DISPID_SVRate, DISPID_SPRuleChildren,
    SP_VISEME_13, SGDSActive, eLEXTYPE_PRIVATE19, SpeechTokenKeyFiles,
    ISpPhoneticAlphabetSelection, eLEXTYPE_VENDORLEXICON,
    SpeechTokenValueCLSID, SPAUDIOSTATUS, IEnumString,
    Speech_StreamPos_RealTime, SVP_17, DISPID_SRRGetXMLResult,
    SPFM_OPEN_READWRITE, SAFTText, DISPID_SPPValue,
    SAFT12kHz8BitStereo, SAFT44kHz8BitMono, SAFTCCITT_ALaw_22kHzMono,
    SAFTCCITT_uLaw_11kHzStereo, DISPID_SRRAudio, DISPID_SGRs_NewEnum,
    IUnknown, DISPID_SDKDeleteValue, DISPID_SRCEAudioLevel,
    SPSNotOverriden, SVPAlert, DISPID_SAFGuid, SPEI_PHONEME,
    DISPID_SRCAudioInInterferenceStatus
)


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


SPAUDIOSTATE = _SPAUDIOSTATE
SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE


__all__ = [
    'DISPID_SLWsCount', 'SAFTADPCM_8kHzStereo', 'SASPause',
    'SPAUDIOBUFFERINFO', 'SpeechSpecialTransitionType',
    'DISPID_SRCEBookmark', 'SAFT16kHz16BitStereo', 'SVSFNLPMask',
    'DISPID_SPPs_NewEnum', 'DISPID_SRState',
    'DISPID_SpeechRecoResult2', 'DISPID_SpeechPhraseRules',
    'DISPID_SLPLangId', 'SVF_None', 'SPVPRI_NORMAL',
    'SAFTGSM610_11kHzMono', 'SpeechRecoEvents', 'ISpeechAudioFormat',
    'ISpSerializeState', 'ISpShortcut', 'SSTTDictation',
    'SpSharedRecoContext', 'SDA_One_Trailing_Space', 'SVSFIsFilename',
    'ISpRecognizer2', 'SAFTCCITT_ALaw_22kHzStereo',
    'SpeechTokenKeyUI', 'DISPID_SAVolume',
    'DISPID_SpeechObjectTokens', 'DISPID_SVEventInterests',
    'SPFM_CREATE', 'SPPS_Unknown', 'DISPID_SVStatus',
    'SAFT48kHz8BitMono', 'SpWaveFormatEx', 'DISPID_SRCPause',
    'eLEXTYPE_PRIVATE11',
    'DISPID_SVAllowAudioOuputFormatChangesOnNextSet',
    'DISPID_SRCCreateGrammar', 'SPRECOSTATE', 'SPVISEMES',
    'DISPID_SpeechPhraseReplacements',
    'SpeechPropertyNormalConfidenceThreshold',
    'DISPID_SRAllowAudioInputFormatChangesOnNextSet', 'SGSDisabled',
    'DISPID_SCSBaseStream',
    'DISPID_SpeechGrammarRuleStateTransitions', 'SPPS_Noun',
    'DISPID_SpeechAudioBufferInfo', 'DISPID_SOTId',
    'DISPID_SPRuleEngineConfidence', 'SDTLexicalForm',
    'SpeechDictationTopicSpelling', 'SpeechCategoryAudioOut',
    'SPPS_SuppressWord', 'DISPID_SLGetGenerationChange',
    'DISPID_SOTDataKey', 'SpeechCategoryVoices',
    'SpeechRecognizerState', 'SPEI_ACTIVE_CATEGORY_CHANGED',
    'SpeechVoiceCategoryTTSRate', 'SPWORDPRONOUNCEABLE',
    'SAFTNoAssignedFormat', 'SGRSTTDictation',
    'SPEI_PROPERTY_NUM_CHANGE', 'SPINTERFERENCE_TOOSLOW',
    'eLEXTYPE_PRIVATE5', 'DISPID_SWFEExtraData', 'ISpRecoContext',
    'SECNormalConfidence', 'DISPID_SPEAudioStreamOffset',
    'DISPID_SpeechGrammarRuleStateTransition',
    'SpeechEmulationCompareFlags', 'DISPID_SLPsItem',
    'DISPID_SPPChildren', 'SDKLDefaultLocation',
    'SPWORDPRONUNCIATIONLIST', 'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE',
    'DISPID_SPRs_NewEnum', 'SPCT_SUB_DICTATION', 'ISpEventSink',
    'SVP_4', 'DISPID_SRGCommit', 'STCInprocHandler',
    'DISPID_SASFreeBufferSpace', 'SAFT11kHz16BitMono',
    'ISpProperties', 'SpeechDataKeyLocation', 'SPEI_RESERVED1',
    'SPRECORESULTTIMES', 'SLTApp',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002',
    'DISPID_SpeechXMLRecoResult', 'ISpeechPhraseProperty',
    'DISPID_SpeechFileStream', 'SpShortcut',
    'DISPID_SRCCmdMaxAlternates', 'SPINTERFERENCE',
    'DISPID_SGRsCommitAndSave', 'SAFT16kHz8BitStereo',
    'DISPID_SpeechObjectToken', 'DISPID_SRAudioInputStream',
    'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN', 'SP_VISEME_7',
    'Speech_StreamPos_Asap', 'SPPS_LMA', 'DISPID_SPRNumberOfElements',
    'SPEI_WORD_BOUNDARY', 'ISpResourceManager', 'SPEI_PHRASE_START',
    'SPBO_PAUSE', 'DISPID_SRRTTickCount', 'SpeechAddRemoveWord',
    'SVSFPersistXML', 'SPDKL_CurrentUser', 'SPLO_STATIC',
    'DISPID_SVGetVoices', 'DISPID_SVESentenceBoundary',
    'SAFT44kHz16BitMono', 'SP_VISEME_12', 'DISPID_SPRsCount',
    '_ISpeechRecoContextEvents', 'SPINTERFERENCE_TOOLOUD',
    'SpeechWordType', 'SRTAutopause', 'SPPHRASERULE', 'SVP_3',
    'DISPID_SABIBufferSize', 'DISPID_SPEActualConfidence',
    'SSSPTRelativeToCurrentPosition', 'SAFTGSM610_44kHzMono',
    'DISPID_SPPFirstElement', 'SRADynamic', 'SpMemoryStream',
    'DISPID_SRCEStartStream', 'SpeechWordPronounceable',
    'DISPID_SPELexicalForm', 'SpPhoneConverter', 'UINT_PTR',
    'DISPID_SRCRetainedAudioFormat', 'DISPID_SGRSTPropertyName',
    'SPEI_SOUND_START', 'DISPID_SGRSTText', 'DISPID_SDKGetlongValue',
    'SPSMF_SRGS_SAPIPROPERTIES', 'DISPID_SRStatus',
    'SpResourceManager', 'DISPID_SpeechVoiceStatus', 'SP_VISEME_3',
    'SSFMOpenReadWrite', 'SPWORDTYPE', 'DISPID_SRGDictationUnload',
    'SREStreamEnd', 'ISpPhoneticAlphabetConverter',
    'DISPID_SPEPronunciation', 'SpeechCategoryAppLexicons',
    'SpeechInterference', 'SpeechCategoryRecoProfiles',
    'SpeechVoiceSpeakFlags', 'SpeechRecoContextState', 'SASStop',
    'DISPID_SVSRunningState', 'SAFT24kHz16BitMono',
    'DISPID_SPIGetDisplayAttributes', 'SPEI_TTS_PRIVATE',
    'SAFTCCITT_uLaw_8kHzMono', 'DISPID_SDKGetStringValue',
    'SECFIgnoreKanaType', 'DISPID_SPEs_NewEnum',
    'eLEXTYPE_LETTERTOSOUND', 'eLEXTYPE_PRIVATE2', 'SDTRule',
    'DISPID_SRCEHypothesis', 'DISPID_SLPsCount', 'ISpXMLRecoResult',
    'DISPID_SPIReplacements', 'SAFTCCITT_uLaw_8kHzStereo',
    'SpeechCategoryAudioIn', 'DISPID_SREmulateRecognition',
    'DISPIDSPTSI_ActiveOffset', 'SpMMAudioOut', 'eLEXTYPE_RESERVED10',
    'DISPID_SRRSpeakAudio', 'SpeechGrammarRuleStateTransitionType',
    'SPSERIALIZEDRESULT', 'SECFDefault', 'SPSERIALIZEDPHRASE',
    'SRTSMLTimeout', 'SPXRO_SML', 'DISPID_SGRsItem',
    'SPEI_INTERFERENCE', 'DISPID_SVEWord', 'SVEAllEvents',
    'ISpeechVoice', 'DISPID_SpeechPhraseProperty',
    'DISPID_SRGCmdLoadFromFile', 'SECFNoSpecialChars',
    'DISPID_SpeechRecoResultTimes', 'ISpRecoGrammar',
    'DISPID_SLAddPronunciationByPhoneIds', 'SVF_Emphasis',
    'DISPID_SPIGetText', 'ISpDataKey', 'SVSFIsNotXML',
    'DISPID_SVAudioOutputStream', 'SPEI_TTS_AUDIO_LEVEL',
    'DISPID_SVWaitUntilDone', 'SREBookmark', 'SpeechAudioVolume',
    'DISPID_SPIRetainedSizeBytes', 'SVP_16',
    'SPWP_UNKNOWN_WORD_PRONOUNCEABLE', 'DISPID_SPPId',
    'SRAORetainAudio', 'SP_VISEME_14', 'SDA_No_Trailing_Space',
    'SPPS_Modifier', 'SPEI_SENTENCE_BOUNDARY', 'DISPID_SRGReset',
    'DISPID_SOTGetAttribute', 'SDTProperty', 'DISPID_SPIElements',
    'ISpRecoCategory', 'SPEI_SR_AUDIO_LEVEL',
    'DISPID_SRCERecognitionForOtherContext', 'SVP_21',
    'ISpeechPhraseRule', '_RemotableHandle',
    'DISPID_SLGetPronunciations', 'DISPID_SRGCmdLoadFromMemory',
    'DISPID_SRSCurrentStreamNumber', 'DISPID_SVVoice',
    'SAFT16kHz8BitMono', 'SPPS_RESERVED2', 'SpInprocRecognizer',
    'SDTAudio', 'DISPID_SOTsItem', 'DISPID_SASNonBlockingIO',
    'eLEXTYPE_RESERVED8', 'SpeechRegistryLocalMachineRoot',
    'SpeechAudioFormatType', 'SVEViseme', 'SLTUser',
    'DISPID_SGRsCount', 'SAFT24kHz16BitStereo', 'SVEStartInputStream',
    'SpeechPropertyAdaptationOn', 'DISPID_SLWs_NewEnum',
    'SPBINARYGRAMMAR', 'SPGS_DISABLED', 'DISPID_SGRSTransitions',
    'SAFTDefault', 'eLEXTYPE_USER_SHORTCUT', 'SAFT8kHz8BitMono',
    'SPVOICESTATUS', 'SPPROPERTYINFO', 'SAFT11kHz8BitStereo',
    'SPAR_Medium', 'SPWT_LEXICAL', 'SREPhraseStart', 'SPAS_PAUSE',
    'DISPID_SpeechPhraseReplacement', 'SpeechRunState',
    'DISPID_SVSInputSentenceLength', 'DISPID_SRGId',
    'DISPID_SRCESoundStart', 'SPEI_END_SR_STREAM', 'DISPID_SPPName',
    'DISPID_SRGRecoContext', 'DISPID_SRRSaveToMemory',
    'DISPID_SVSVisemeId', 'SAFT48kHz8BitStereo', 'SPPS_RESERVED1',
    'SPPS_Verb', 'SPXRO_Alternates_SML', 'DISPID_SAFGetWaveFormatEx',
    'DISPID_SDKGetBinaryValue', 'DISPID_SRGIsPronounceable',
    'SpeechPropertyComplexResponseSpeed', 'SPSHORTCUTPAIR',
    'ISpPhoneConverter', 'SLOStatic', 'SBONone', 'DISPID_SRCResume',
    'SPEI_START_SR_STREAM', 'SpPhraseInfoBuilder', 'SP_VISEME_20',
    'DISPID_SRGCmdLoadFromResource', 'SP_VISEME_19',
    'SGPronounciation', 'DISPID_SPRFirstElement',
    'SAFTCCITT_uLaw_44kHzStereo', 'SAFT32kHz16BitStereo',
    'SpeechRecoProfileProperties', 'DISPID_SLWType',
    'DISPID_SOTRemoveStorageFileName', 'SINoSignal',
    'SDTPronunciation', 'SREStateChange', 'SPRST_ACTIVE',
    'SpeechRetainedAudioOptions', 'SpeechDiscardType',
    'DISPID_SPIEngineId', 'SRTReSent', 'SVP_12', 'DISPID_SRCBookmark',
    'SPPHRASEELEMENT', 'SPRS_INACTIVE', 'SPINTERFERENCE_NOISE',
    'SPRECOGNIZERSTATUS', 'eLEXTYPE_PRIVATE14', 'SPCT_DICTATION',
    'ISpeechObjectTokens', 'SAFT24kHz8BitStereo',
    'ISpeechWaveFormatEx', 'ISpeechMemoryStream',
    'DISPID_SRCEInterference', 'SpeechCategoryPhoneConverters',
    'SFTInput', 'DISPID_SpeechLexiconWords',
    'SPINTERFERENCE_LATENCY_TRUNCATE_END', 'ISpAudio',
    'SGDSActiveWithAutoPause', 'DISPID_SPEEngineConfidence',
    'eLEXTYPE_PRIVATE8', 'DISPID_SRSAudioStatus', 'SPPHRASEPROPERTY',
    'eLEXTYPE_PRIVATE18', 'ISpeechRecoContext',
    'DISPID_SRCERecognition', 'SP_VISEME_5', 'DISPID_SDKDeleteKey',
    'ISpeechMMSysAudio', 'SPWORDLIST', 'SWTDeleted',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C', 'SPAR_Low',
    'SRTStandard', 'SPDKL_LocalMachine',
    'DISPID_SpeechGrammarRuleState', 'DISPID_SpeechLexiconProns',
    'DISPID_SRSClsidEngine', 'SPEI_TTS_BOOKMARK', 'SVSFParseSsml',
    'SPEI_PROPERTY_STRING_CHANGE', 'ISpNotifySink',
    'SpeechAudioFormatGUIDText',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_MS',
    'SpeechPropertyResponseSpeed', 'ISpeechFileStream',
    'IEnumSpObjectTokens', 'DISPID_SRCreateRecoContext',
    'DISPID_SRCEAdaptation', 'SVEPhoneme', 'DISPID_SVSLastBookmarkId',
    'SpeechAudioProperties', 'DISPID_SLPs_NewEnum', 'SPSFunction',
    'SAFTNonStandardFormat', 'DISPID_SPPsItem', 'SPRST_ACTIVE_ALWAYS',
    'DISPID_SPPsCount', 'DISPID_SLWsItem', 'ISpeechRecoResult2',
    'eLEXTYPE_RESERVED9', 'ISpRecoGrammar2',
    'DISPID_SRAllowVoiceFormatMatchingOnNextSet',
    'SpeechPartOfSpeech', 'SPFM_NUM_MODES', 'SSFMCreate',
    'DISPID_SPACommit', 'ISpeechObjectToken', 'SP_VISEME_15',
    'DISPID_SVEVoiceChange', 'SP_VISEME_16', 'DISPID_SRCESoundEnd',
    'ISpeechGrammarRuleStateTransition', 'DISPID_SAStatus',
    'DISPID_SLWWord', 'SVSFParseAutodetect', 'SPEI_RESERVED5',
    'SPSMF_SAPI_PROPERTIES', 'SRSEIsSpeaking', 'SPCT_SUB_COMMAND',
    'DISPID_SRCRequestedUIType', 'SAFT8kHz16BitStereo',
    'SpeechGrammarTagDictation', 'SpeechTokenKeyAttributes',
    'SpMMAudioEnum', 'SRCS_Enabled', 'SPSModifier',
    'SpeechStreamSeekPositionType', 'DISPID_SpeechWaveFormatEx',
    'DISPID_SPAs_NewEnum', 'DISPID_SOTRemove', 'SAFTADPCM_8kHzMono',
    'SRESoundEnd', 'ISpNotifySource', 'DISPID_SGRSTPropertyId',
    'SGDisplay', 'SPRS_ACTIVE_WITH_AUTO_PAUSE', 'SRARoot',
    'SRTExtendableParse', 'ISpeechGrammarRules',
    'DISPID_SGRAttributes', 'SPEI_HYPOTHESIS', 'STCLocalServer',
    'DISPID_SLRemovePronunciationByPhoneIds', 'SRERequestUI',
    'DISPID_SPIGrammarId', 'DISPID_SFSClose',
    'SpTextSelectionInformation', 'DISPID_SLRemovePronunciation',
    'DISPID_SRCSetAdaptationData', 'DISPID_SGRSTWeight',
    'SPPS_Interjection', 'DISPID_SRCEPhraseStart', 'DISPID_SPRuleId',
    'DISPID_SRCVoice', 'ISpeechRecognizerStatus', 'SGSEnabled',
    'SDTAll', 'SAFTExtendedAudioFormat', 'eLEXTYPE_PRIVATE3',
    'SVP_15', 'SPBOOKMARKOPTIONS', 'DISPID_SVSInputWordPosition',
    'SPWAVEFORMATTYPE', 'LONG_PTR', 'DISPID_SPEDisplayAttributes',
    'DISPID_SRGetPropertyString', 'eWORDTYPE_ADDED', 'ISpeechLexicon',
    'typelib_path', 'SPWT_DISPLAY', 'SPAO_RETAIN_AUDIO',
    'ISpeechRecoResult', 'SPAR_High', 'DISPID_SOTCreateInstance',
    'eLEXTYPE_USER', 'DISPID_SPIAudioSizeTime',
    'DISPID_SRSetPropertyNumber', 'SpeechPropertyResourceUsage',
    'SAFTCCITT_ALaw_44kHzMono', 'SAFT22kHz16BitStereo',
    'SpSharedRecognizer', 'eLEXTYPE_PRIVATE17', 'SPCS_DISABLED',
    'DISPID_SAFSetWaveFormatEx', 'SPEI_VISEME', 'SPTEXTSELECTIONINFO',
    'SGLexical', 'SpeechDisplayAttributes', 'SRESoundStart',
    'SpeechVoiceSkipTypeSentence', 'SVSFNLPSpeakPunc',
    'SDTAlternates', 'SAFTCCITT_ALaw_11kHzStereo',
    'SPINTERFERENCE_TOOQUIET', 'DISPID_SVSCurrentStreamNumber',
    'DISPID_SVSLastBookmark', 'DISPID_SLPType', 'DISPID_SRGetFormat',
    'SRSInactive', 'DISPID_SASState', 'SGRSTTWord',
    'DISPID_SOTs_NewEnum', 'DISPID_SOTCGetDataKey',
    'SECFEmulateResult', 'DISPID_SVAlertBoundary',
    'SpPhoneticAlphabetConverter', 'DISPID_SGRAddState',
    'SAFT12kHz16BitMono', 'DISPID_SVSInputWordLength',
    'DISPID_SGRSAddWordTransition', 'SPEI_SR_RETAINEDAUDIO',
    'DISPID_SpeechGrammarRule', 'ISpStreamFormat',
    'DISPID_SDKSetLongValue', 'DISPID_SpeechBaseStream',
    'SPVPRI_OVER', 'SPLO_DYNAMIC', 'SVSFVoiceMask', 'SPGS_EXCLUSIVE',
    'SPCS_ENABLED', 'SECLowConfidence', 'DISPID_SBSFormat',
    'SPSHT_Unknown', 'ISpEventSource', 'SVEAudioLevel',
    'DISPID_SLAddPronunciation', 'SPWT_PRONUNCIATION',
    'ISpObjectToken', 'SAFT32kHz8BitStereo', 'SRAONone',
    'SAFTCCITT_uLaw_11kHzMono', 'DISPID_SRRDiscardResultInfo',
    'DISPID_SpeechPhoneConverter', 'SpCompressedLexicon',
    'SPAS_CLOSED', 'SPRULESTATE', 'SpeechGrammarWordType',
    'eLEXTYPE_PRIVATE1', 'ISpeechAudioStatus', 'DISPID_SPAsItem',
    'SPEI_RESERVED2', 'SPEI_MIN_SR', 'DISPID_SPPParent',
    'ISpObjectTokenCategory', 'DISPID_SPANumberOfElementsInResult',
    'SpeechBookmarkOptions', 'SAFT32kHz8BitMono', 'DISPID_SVSkip',
    'DISPID_SRGRules', 'SAFT22kHz8BitStereo',
    'DISPID_SVSyncronousSpeakTimeout', 'eLEXTYPE_PRIVATE4',
    'SpeechGrammarState', 'SVP_18', 'SREPropertyNumChange',
    'SpeechVisemeFeature', 'DISPID_SVIsUISupported', 'STSF_AppData',
    'SPVPRIORITY', 'eLEXTYPE_PRIVATE10', 'DISPID_SWFEBitsPerSample',
    'DISPID_SPPConfidence', 'SPSMF_UPS', 'SPWORDPRONUNCIATION',
    'DISPID_SVSLastResult', 'DISPID_SRGCmdLoadFromObject',
    'DISPID_SRRTOffsetFromStart', 'SPBO_TIME_UNITS',
    'DISPID_SpeechCustomStream', 'DISPID_SVEBookmark', 'SpMMAudioIn',
    'SVSFPurgeBeforeSpeak', 'SECHighConfidence', 'WAVEFORMATEX',
    'SVP_9', 'DISPID_SMSGetData', 'SVP_20', 'DISPID_SGRSTNextState',
    'eLEXTYPE_PRIVATE15', 'SAFT22kHz16BitMono',
    'SpeechEngineConfidence', 'DISPID_SPCIdToPhone',
    'SAFT11kHz8BitMono', 'SVEWordBoundary', 'SRATopLevel',
    'SWPUnknownWordPronounceable', 'SPFM_OPEN_READONLY',
    'tagSPTEXTSELECTIONINFO', 'DISPID_SPPBRestorePhraseFromMemory',
    'eLEXTYPE_PRIVATE13', 'DISPID_SRCEEndStream',
    'DISPID_SPRules_NewEnum', 'SPWF_INPUT', 'SPSTREAMFORMATTYPE',
    'ISpeechPhraseInfo', 'SPDATAKEYLOCATION', 'DISPID_SPILanguageId',
    'SPSHT_OTHER', 'SVP_7', 'DISPID_SGRAddResource',
    'SAFTGSM610_22kHzMono', 'DISPID_SPPNumberOfElements',
    'SPGS_ENABLED', 'SVEPrivate', 'SAFT8kHz16BitMono', 'SVSFDefault',
    'SREAllEvents', 'DISPID_SBSSeek', 'DISPID_SpeechRecognizer',
    'DISPID_SRCERequestUI', 'SFTSREngine', 'SPAR_Unknown',
    'DISPID_SWFESamplesPerSec', 'DISPID_SPIStartTime',
    'SPEI_RECOGNITION', 'SP_VISEME_0', 'ISpeechPhraseRules',
    'DISPID_SPIProperties', 'SSSPTRelativeToEnd', 'SPEI_MAX_TTS',
    'SpeechMicTraining', 'SDKLLocalMachine',
    'DISPID_SGRSTPropertyValue', 'DISPID_SOTGetStorageFileName',
    'STSF_LocalAppData', 'SPRECOCONTEXTSTATUS',
    'DISPID_SVSLastStreamNumberQueued', 'SVP_13', 'DISPID_SGRsAdd',
    'DISPID_SPAsCount', 'SPINTERFERENCE_NOSIGNAL',
    'SAFT12kHz16BitStereo', 'SPRS_ACTIVE_USER_DELIMITED',
    'DISPID_SRRPhraseInfo', 'SDKLCurrentUser',
    'DISPID_SABufferNotifySize', 'SREFalseRecognition',
    'DISPID_SVAudioOutput', 'SpeechAudioState', 'DISPID_SRProfile',
    'DISPID_SGRInitialState', 'DISPID_SRSetPropertyString',
    'Speech_Max_Word_Length', 'SPRST_INACTIVE_WITH_PURGE',
    'SpInProcRecoContext', 'SP_VISEME_1',
    'DISPID_SRSCurrentStreamPosition', 'ISpeechPhraseElement',
    'DISPID_SGRsDynamic', 'DISPID_SpeechMMSysAudio',
    'DISPID_SLPPhoneIds', 'SpeechPropertyLowConfidenceThreshold',
    'IStream', 'DISPID_SPRuleParent', 'DISPID_SRGCmdSetRuleState',
    'ISpRecognizer3', 'DISPID_SBSWrite', 'SpStream',
    'ISpeechRecoGrammar', 'IInternetSecurityManager', 'SP_VISEME_11',
    'DISPID_SpeechLexiconWord', 'SDTReplacement',
    'eLEXTYPE_PRIVATE20', 'SAFT22kHz8BitMono', 'DISPID_SPIRule',
    'DISPID_SRRTLength', 'Speech_Max_Pron_Length', 'DISPID_SPEsCount',
    'SINone', 'SVP_6', 'DISPID_SPRuleName', 'SPAO_NONE',
    'DISPID_SOTCDefault', 'STCRemoteServer', 'SBOPause',
    'ISpeechLexiconWords', 'DISPID_SVGetAudioOutputs',
    'DISPID_SWFEAvgBytesPerSec', 'ISpRecoResult', 'SPSNoun',
    'SPEI_SR_PRIVATE', 'DISPID_SOTCId', 'SPSSuppressWord',
    'SPEI_RESERVED6', 'SVESentenceBoundary', 'SPSHORTCUTPAIRLIST',
    'DISPID_SVPriority', 'ISpeechAudioBufferInfo',
    'SVEEndInputStream', 'SPPS_Function',
    'DISPID_SpeechLexiconPronunciation', 'ISpeechGrammarRule',
    'DISPID_SRCVoicePurgeEvent', 'SPCONTEXTSTATE', 'tagSTATSTG',
    'DISPID_SpeechPhraseAlternates', 'DISPID_SAFType',
    'DISPID_SPRDisplayAttributes',
    'ISpeechGrammarRuleStateTransitions', 'SPINTERFERENCE_TOOFAST',
    'SPINTERFERENCE_LATENCY_WARNING', 'DISPID_SRCEventInterests',
    'eLEXTYPE_RESERVED6', 'SP_VISEME_4', 'SREPropertyStringChange',
    'SPEI_MAX_SR', 'SGRSTTTextBuffer', 'DISPID_SGRsFindRule',
    'SITooLoud', 'DISPID_SRCEEnginePrivate',
    'DISPID_SpeechPhraseProperties', 'DISPID_SPRsItem',
    'SpeechTokenShellFolder', 'SPXMLRESULTOPTIONS',
    'DISPID_SRGDictationSetState', 'tagSPPROPERTYINFO',
    'DISPID_SVEPhoneme', 'DISPID_SWFEChannels', 'SPEI_SOUND_END',
    'SpeechPropertyHighConfidenceThreshold', 'SpLexicon',
    'SAFT44kHz8BitStereo', 'ISpeechAudio',
    'SWPUnknownWordUnpronounceable', 'DISPID_SVSPhonemeId',
    'DISPID_SPEDisplayText', 'DISPID_SGRSTType',
    'SSSPTRelativeToStart', 'DISPID_SPRulesItem',
    'DISPID_SWFEBlockAlign', 'ISpeechPhraseInfoBuilder',
    'DISPID_SASetState', 'ISpObjectWithToken', 'STSF_CommonAppData',
    'DISPID_SPRulesCount', 'DISPID_SOTsCount', 'SPRULE',
    'SpCustomStream', 'SPRS_ACTIVE', 'DISPID_SOTSetId',
    'DISPID_SpeechPhraseRule', 'SPRST_NUM_STATES',
    'ISpeechPhraseElements', 'DISPID_SOTCSetId', 'SPPS_NotOverriden',
    'DISPID_SpeechPhraseElement', 'DISPID_SRGSetWordSequenceData',
    'SGSExclusive', 'SpeechLexiconType', 'SpeechTokenContext',
    'SAFT44kHz16BitStereo', 'ISpStreamFormatConverter',
    'SPEI_MIN_TTS', 'SPSLMA', 'SpeechTokenIdUserLexicon',
    'SSTTTextBuffer', 'SPEI_ADAPTATION',
    'DISPID_SPERequiredConfidence', 'DISPID_SPERetainedStreamOffset',
    'DISPID_SFSOpen', 'DISPID_SRCState', 'DISPID_SRGetRecognizers',
    'DISPID_SpeechPhraseAlternate', 'DISPID_SRAudioInput',
    'DISPID_SVPause', 'DISPID_SPISaveToMemory', 'SRERecognition',
    'DISPID_SpeechAudioFormat', 'DISPID_SPIAudioStreamPosition',
    'DISPID_SVEViseme', 'SpeechAudioFormatGUIDWave', 'SPBO_NONE',
    'SPDKL_DefaultLocation', 'DISPID_SASCurrentSeekPosition',
    'DISPID_SVSInputSentencePosition', 'SP_VISEME_9',
    'DISPID_SpeechAudioStatus', 'DISPID_SOTIsUISupported',
    'DISPID_SMSSetData', 'SAFTCCITT_ALaw_8kHzStereo',
    'SPPS_RESERVED4', 'SpeechUserTraining', 'SVP_19',
    'SPEVENTSOURCEINFO', 'DISPID_SRRTimes', 'ISpRecognizer',
    'ISpVoice', 'SVSFParseMask', 'DISPID_SRCEFalseRecognition',
    'DISPID_SRRSetTextFeedback', 'SPWORD', 'DISPID_SPEsItem',
    'DISPID_SPCPhoneToId', 'SVPNormal', 'DISPID_SRGCmdSetRuleIdState',
    'SITooFast', 'SPEI_RECO_OTHER_CONTEXT', 'SPPS_RESERVED3',
    'SPAS_STOP', 'SAFTCCITT_uLaw_22kHzMono', 'SPSVerb',
    'SAFTCCITT_uLaw_44kHzMono', 'DISPID_SABIMinNotification',
    'SpeechRecognitionType', 'SpeechGrammarTagUnlimitedDictation',
    'DISPID_SVEStreamStart', 'SVP_10', 'SpStreamFormatConverter',
    'ISpeechXMLRecoResult', 'SAFT32kHz16BitMono', 'SPSInterjection',
    'DISPID_SWFEFormatTag', 'SGRSTTEpsilon', 'eLEXTYPE_PRIVATE12',
    'SAFT48kHz16BitStereo', 'SP_VISEME_6',
    'ISpeechPhraseReplacements', 'SAFTCCITT_ALaw_8kHzMono',
    'SITooSlow', 'ISpeechObjectTokenCategory', 'DISPID_SBSRead',
    'DISPID_SPRuleConfidence', 'DISPID_SOTCategory',
    'SpeechRuleState', 'SREAdaptation', 'SVSFlagsAsync',
    'DISPID_SLWLangId', 'DISPID_SGRSTsItem', 'ISpLexicon',
    'ISpeechLexiconPronunciations', 'ISpeechBaseStream',
    'SPAUDIOOPTIONS', 'SPEVENT', 'SpeechEngineProperties',
    'DISPID_SDKEnumValues', 'SSFMOpenForRead', 'SpeechVisemeType',
    'SpUnCompressedLexicon', 'DISPID_SVSpeakCompleteEvent',
    'DISPID_SGRClear', 'SRAImport', 'SGDSInactive', 'DISPID_SRGState',
    'DISPIDSPTSI_ActiveLength', 'DISPID_SRIsUISupported',
    'DISPID_SRGSetTextSelection', 'SPPARTOFSPEECH',
    'DISPID_SMSALineId', 'DISPID_SVSpeakStream',
    '__MIDL_IWinTypes_0009', 'SAFT24kHz8BitMono', 'SINoise',
    'ISpeechVoiceStatus', 'DISPID_SRGetPropertyNumber', 'SVP_1',
    'DISPID_SVResume', 'SP_VISEME_2', 'SPAUDIOSTATE',
    'SpeechVoiceEvents', 'ISpeechCustomStream',
    'DISPID_SPAPhraseInfo', 'DISPID_SLPSymbolic', 'DISPID_SGRName',
    'SRSEDone', 'DISPID_SVGetProfiles', 'SPPHRASE',
    'ISpeechRecognizer', 'SAFTADPCM_11kHzMono',
    'ISpeechPhraseAlternates', 'DISPID_SVGetAudioInputs',
    'DISPID_SRSSupportedLanguages', 'DISPID_SPIAudioSizeBytes',
    'DISPID_SADefaultFormat', 'DISPID_SRRAlternates',
    'DISPID_SpeechPhraseElements', 'ISpeechPhraseReplacement',
    'ISpeechResourceLoader', 'SRSActive',
    'ISpeechTextSelectionInformation', 'DISPID_SGRsCommit',
    'DISPID_SPARecoResult', 'DISPID_SDKOpenKey', 'SRSActiveAlways',
    'SpVoice', 'DISPID_SVEStreamEnd', 'SPEI_RESERVED3', 'SRTEmulated',
    'SPEVENTENUM', 'ISpeechRecoResultTimes',
    'DISPIDSPTSI_SelectionLength', 'SPEI_FALSE_RECOGNITION',
    'SRSInactiveWithPurge', 'DISPID_SPRText', 'SpNotifyTranslator',
    'ISpeechPhraseProperties', 'SpNullPhoneConverter',
    'DISPID_SMSADeviceId', 'SAFTGSM610_8kHzMono',
    'DISPID_SRRTStreamTime', 'STCAll', 'SGRSTTRule',
    'DISPID_SABIEventBias', 'DISPID_SPERetainedSizeBytes',
    'SSFMCreateForWrite', 'SPPS_Noncontent', 'DISPID_SGRId',
    'SAFT11kHz16BitStereo', 'SAFTTrueSpeech_8kHz1BitMono',
    'DISPID_SVEAudioLevel', 'SAFT48kHz16BitMono',
    'DISPID_SPEAudioSizeBytes', 'SVEBookmark',
    'DISPID_SOTCEnumerateTokens', 'SPEI_SR_BOOKMARK', 'SPLOADOPTIONS',
    'SAFTCCITT_ALaw_44kHzStereo', 'ISpPhraseAlt',
    '__MIDL___MIDL_itf_sapi_0000_0020_0001',
    'DISPID_SpeechMemoryStream', 'DISPID_SPRuleNumberOfElements',
    'ISpRecoContext2', 'DISPID_SGRSTsCount', 'SREHypothesis',
    'ISpeechPhoneConverter', 'SPVPRI_ALERT', 'SpeechVoicePriority',
    'ISpeechGrammarRuleState', 'SVP_0',
    'DISPID_SRCCreateResultFromMemory', '_SPAUDIOSTATE',
    'SPEI_VOICE_CHANGE', 'DISPID_SRRAudioFormat', 'SASClosed',
    'DISPID_SDKSetBinaryValue', 'SPSHT_NotOverriden',
    '_ISpeechVoiceEvents', 'SPSUnknown', 'SGRSTTWildcard',
    'SRAInterpreter', 'SPGRAMMARSTATE', 'DISPID_SPIEnginePrivateData',
    'DISPID_SpeechPhraseInfo', 'ISpeechRecoResultDispatch',
    'DISPID_SAEventHandle', 'SP_VISEME_17', 'DISPID_SRRRecoContext',
    'SPSHORTCUTTYPE', 'DISPID_SVDisplayUI',
    'DISPID_SOTGetDescription', 'DISPID_SRSNumberOfActiveRules',
    'SPEI_START_INPUT_STREAM', 'SPCATEGORYTYPE', 'SVP_2',
    'DISPID_SDKCreateKey', 'SVP_14', 'eWORDTYPE_DELETED',
    'SPSEMANTICFORMAT', 'SGLexicalNoSpecialChars',
    'ISpeechLexiconPronunciation', 'SDA_Consume_Leading_Spaces',
    'DISPID_SpeechPhraseBuilder', 'SLODynamic',
    'SpeechRegistryUserRoot', 'DISPID_SGRSAddSpecialTransition',
    'ISpeechDataKey', 'SVPOver', 'SAFTCCITT_ALaw_11kHzMono',
    'DISPID_SGRSAddRuleTransition', 'SP_VISEME_21',
    'DISPID_SVEEnginePrivate', 'eLEXTYPE_APP', 'SVEVoiceChange',
    'SECFIgnoreWidth', 'DISPID_SVVolume', 'DISPID_SRDisplayUI',
    'DISPID_SOTDisplayUI', 'SpeechFormatType', 'DISPID_SRCRecognizer',
    'SWTAdded', 'SREInterference', 'SVP_11', 'SSTTWildcard',
    'DISPID_SASCurrentDevicePosition', 'DISPIDSPRG', 'SP_VISEME_18',
    'DISPID_SVSpeak', 'DISPID_SPEAudioSizeTime', 'SpFileStream',
    'DISPID_SPAStartElementInResult', 'SpeechGrammarTagWildcard',
    'SPBO_AHEAD', 'SAFTADPCM_44kHzMono', 'SDKLCurrentConfig',
    'STSF_FlagCreate', 'DISPID_SPPEngineConfidence',
    'DISPID_SLGetWords', 'DISPID_SpeechAudio',
    'ISpeechPhraseAlternate', 'eLEXTYPE_PRIVATE9',
    'DISPID_SRCRetainedAudio', 'SPWT_LEXICAL_NO_SPECIAL_CHARS',
    'SREPrivate', 'SVSFUnusedFlags', 'DISPID_SGRSTs_NewEnum',
    'DISPID_SLWPronunciations', 'DISPID_SDKSetStringValue',
    'SPLEXICONTYPE', 'SPSHT_EMAIL', 'DISPID_SABufferInfo',
    'DISPIDSPTSI', 'SpeechLoadOption', 'SpAudioFormat',
    'DISPID_SRRGetXMLErrorInfo', 'SPCT_SLEEP', 'SVF_Stressed',
    'DISPID_SLPPartOfSpeech', 'SP_VISEME_10', 'DISPID_SGRSTRule',
    'SPPHRASEREPLACEMENT', 'DISPID_SOTMatchesAttributes',
    'SPWP_KNOWN_WORD_PRONOUNCEABLE', 'DISPID_SpeechRecoResult',
    'eLEXTYPE_PRIVATE7', 'SRAExport', 'DISPID_SRGDictationLoad',
    'SPEI_REQUEST_UI', 'DISPID_SpeechLexicon', 'SRADefaultToActive',
    'eLEXTYPE_RESERVED7', 'SAFT8kHz8BitStereo',
    'SPADAPTATIONRELEVANCE', 'SPRST_INACTIVE',
    'DISPID_SPEAudioTimeOffset', 'ISpGrammarBuilder',
    'DISPID_SRCERecognizerStateChange', 'DISPID_SDKEnumKeys',
    'SpeechRuleAttributes', 'DISPID_SpeechGrammarRules',
    'SAFT16kHz16BitMono', 'DISPID_SLGenerationId', 'DISPID_SGRSRule',
    'eLEXTYPE_PRIVATE6', 'ISpNotifyTranslator',
    'SPEI_END_INPUT_STREAM', 'SpeechCategoryRecognizers', 'Library',
    'SREAudioLevel', 'DISPID_SPRuleFirstElement',
    'SpObjectTokenCategory', 'SPEI_UNDEFINED', 'DISPID_SpeechDataKey',
    'SECFIgnoreCase', 'SGDSActiveUserDelimited',
    'SPSEMANTICERRORINFO', 'SPAS_RUN', 'SAFT12kHz8BitMono',
    'SVSFParseSapi', 'STCInprocServer', 'SVP_8',
    'IInternetSecurityMgrSite', 'SASRun', 'eLEXTYPE_MORPHOLOGY',
    'SpeechAllElements', 'DISPIDSPTSI_SelectionOffset',
    'SpObjectToken', 'SREStreamStart', 'DISPID_SRRecognizer',
    'SPFM_CREATE_ALWAYS', 'SPCT_COMMAND', 'DISPID_SMSAMMHandle',
    'SRCS_Disabled', 'SpeechStreamFileMode',
    'DISPID_SpeechRecoContext', 'DISPID_SRCEPropertyStringChange',
    'DISPID_SpeechVoice', 'DISPID_SVRate',
    'DISPID_SpeechRecognizerStatus', 'SDTDisplayText',
    'SWPKnownWordPronounceable', 'DISPID_SpeechRecoContextEvents',
    'SPWF_SRENGINE', 'DISPID_SPRuleChildren', 'SGDSActive',
    'eLEXTYPE_PRIVATE19', 'DISPID_SRIsShared', 'SpeechTokenKeyFiles',
    'ISpPhoneticAlphabetSelection', 'DISPID_SpeechVoiceEvent',
    'SP_VISEME_13', 'eLEXTYPE_VENDORLEXICON', 'SpeechTokenValueCLSID',
    'SPFILEMODE', 'ISpeechLexiconWord', 'eLEXTYPE_PRIVATE16',
    'SPAUDIOSTATUS', 'IEnumString', 'eLEXTYPE_RESERVED4',
    'SDA_Two_Trailing_Spaces', 'Speech_StreamPos_RealTime',
    'SP_VISEME_8', 'DISPID_SpeechObjectTokenCategory', 'SVSFIsXML',
    'SAFTADPCM_22kHzMono', 'SVP_17', 'DISPID_SRRGetXMLResult',
    'ISpMMSysAudio', 'SPFM_OPEN_READWRITE', 'SAFTText',
    'SPINTERFERENCE_NONE', 'DISPID_SPPValue',
    'SAFTCCITT_uLaw_22kHzStereo', 'SAFT12kHz8BitStereo',
    'SAFT44kHz8BitMono', 'SAFTADPCM_44kHzStereo', 'SITooQuiet',
    'SPDKL_CurrentConfig', 'DISPID_SRGCmdLoadFromProprietaryGrammar',
    'SAFTCCITT_ALaw_22kHzMono', 'SAFTCCITT_uLaw_11kHzStereo',
    'SAFTADPCM_22kHzStereo', 'SPEI_RECO_STATE_CHANGE',
    'DISPID_SRRAudio', 'SPGRAMMARWORDTYPE', 'SVP_5',
    'DISPID_SGRs_NewEnum', 'DISPID_SDKDeleteValue', 'ISpPhrase',
    'DISPID_SRCEAudioLevel', 'SPSNotOverriden',
    'Speech_Default_Weight', 'DISPID_SRCEPropertyNumberChange',
    'ISpStream', 'SVPAlert', 'DISPID_SAFGuid',
    'SAFTADPCM_11kHzStereo', 'DISPID_SPCLangId', 'SPEI_PHONEME',
    'SRERecoOtherContext', 'DISPID_SRCAudioInInterferenceStatus'
]

